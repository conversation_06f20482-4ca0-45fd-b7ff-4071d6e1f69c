#include "file_handler.h"
#include <Arduino.h> // 确保核心库被包含
#include "FS.h"
#include "SPIFFS.h"
#include <ArduinoJson.h>
#include "user_config.h"  // 添加这行以包含curve_t定义
#include <BLECharacteristic.h> // 引入BLECharacteristic头文件
// SPP蓝牙已禁用
// #include <BluetoothSerial.h> // 添加蓝牙串口头文件

// 变量定义已移至主文件，此处不再需要

bool init_fs()
{
    // 先尝试非强制挂载
    if (!SPIFFS.begin(false)) {
        Serial.println("SPIFFS 非强制挂载失败，尝试强制挂载...");
        if (!SPIFFS.begin(true)) {
            Serial.println("SPIFFS 强制挂载失败");
            return false;
        }
    }

    // 检查文件系统是否已格式化
    if (!SPIFFS.exists("/config.json")) {
        Serial.println("文件系统未格式化，正在格式化...");
        
        // 格式化文件系统
        if (!SPIFFS.format()) {
            Serial.println("文件系统格式化失败");
            return false;
        }
        
        // 重新挂载
        if (!SPIFFS.begin(true)) {
            Serial.println("格式化后 SPIFFS 挂载失败");
            return false;
        }
        
        // 验证关键文件是否可创建
        File testFile = SPIFFS.open("/config.json", "w");
        if (!testFile) {
            Serial.println("格式化后无法创建关键文件");
            return false;
        }
        testFile.close();
        
        Serial.println("文件系统已成功格式化并验证");
    }

    // 检查文件系统空间
    size_t totalBytes = SPIFFS.totalBytes();
    size_t usedBytes = SPIFFS.usedBytes();
    size_t freeBytes = totalBytes - usedBytes;
    
    Serial.printf("SPIFFS 总空间: %u 字节\n", totalBytes);
    Serial.printf("SPIFFS 已使用空间: %u 字节\n", usedBytes);
    Serial.printf("SPIFFS 可用空间: %u 字节\n", freeBytes);
    
    // 检查可用空间是否足够(至少保留10KB)
    const size_t MIN_FREE_SPACE = 10 * 1024; // 10KB
    if (freeBytes < MIN_FREE_SPACE) {
        Serial.println("警告: 文件系统可用空间不足");
        return false;
    }
    
    return true;
}

int save_config_json(const JsonDocument& config)
{
    Serial.println("正在保存JSON配置...");
    File configFile = SPIFFS.open("/config.json", "w");
    if (!configFile)
    {
        Serial.println("打开配置文件失败");
        return 0;
    }
    
    int bytesWritten = serializeJson(config, configFile);
    configFile.close();
    
    Serial.println("配置已保存为JSON格式");
    return bytesWritten;
}

int load_config_json(JsonDocument& config)
{
    Serial.println("正在加载JSON配置...");
    
    // 检查文件是否存在
    if(!SPIFFS.exists("/config.json")) {
        Serial.println("错误: 配置文件/config.json不存在");
        return 0;
    }
    
    File configFile = SPIFFS.open("/config.json", "r");
    if (!configFile)
    {
        Serial.println("错误: 打开配置文件失败");
        return 0;
    }
    
    // 检查文件大小
    size_t configSize = configFile.size();
    if(configSize == 0) {
        Serial.println("错误: 配置文件为空");
        configFile.close();
        return 0;
    }
    
    // 检查文件内容是否以'{'开头(基本JSON格式检查)
    if(configFile.peek() != '{') {
        Serial.println("错误: 文件不是有效的JSON格式");
        configFile.close();
        return 0;
    }
    
    DeserializationError error = deserializeJson(config, configFile);
    if (error)
    {
        Serial.print("错误: JSON解析失败 - ");
        Serial.println(error.c_str());
        Serial.println("文件内容:");
        configFile.seek(0);
        while(configFile.available())
        {
            Serial.write(configFile.read());
        }
        Serial.println();
        configFile.close();
        return 0;
    }
    
    configFile.close();
    Serial.println("配置已从JSON文件加载");
    return configSize;
}

int save_curve(int curve_id, const curve_t* source_curve, int source_index) {
    // 使用静态变量作为默认值
    const curve_t* save_source = source_curve ? source_curve : DataCurve;
    int index = source_index >= 0 ? source_index : curveIndex;

    Serial.println("正在保存曲线...");

    String filename = "/curve" + String(curve_id) + ".json";
    File curveFile = SPIFFS.open(filename, "w");
    if (!curveFile) {
        Serial.println("打开曲线文件失败");
        return 0;
    }

    StaticJsonDocument<2048> doc;

    // 构建 info 描述字段
    JsonObject info = doc.createNestedObject("info");
    info["id"] = curve_id;
    info["description"] = DataDescription;
    info["points"] = index;

    // 曲线数据数组
    JsonArray data = doc.createNestedArray("data");

    for (int i = 0; i < index; i++) {
        JsonObject point = data.createNestedObject();
        point["t"] = save_source[i].t;
        point["bt"] = save_source[i].bt;
        point["fir"] = save_source[i].fir;
        point["fan"] = save_source[i].fan;
    }

    serializeJson(doc, curveFile);
    curveFile.close();

    Serial.println("曲线已保存为 JSON 格式: " + filename);
    return index;  // 返回保存的曲线点数量
}

int load_curve(int curve_id, curve_t* target_curve, int* target_index) {
    // 使用静态变量作为默认值
    curve_t* load_target = target_curve ? target_curve : DataCurve;
    int* index_target = target_index ? target_index : &curveIndex;

    String filename = "/curve" + String(curve_id) + ".json";
    
    // 检查文件是否存在
    if(!SPIFFS.exists(filename))
    {
        Serial.println("曲线文件不存在: " + filename);
        return false;
    }

    File curveFile = SPIFFS.open(filename, "r");
    if (!curveFile || curveFile.size() == 0)
    {
        Serial.println("打开曲线文件失败或文件为空");
        return false;
    }

    // 检查文件内容是否以'{'开头(基本JSON格式检查)
    if(curveFile.peek() != '{')
    {
        Serial.println("文件不是有效的JSON格式");
        curveFile.close();
        return false;
    }

    StaticJsonDocument<4096> doc;
    DeserializationError error = deserializeJson(doc, curveFile);
    if (error)
    {
        Serial.print("JSON解析错误: ");
        Serial.println(error.c_str());
        Serial.print("文件内容: ");
        curveFile.seek(0);
        while(curveFile.available())
        {
            Serial.write(curveFile.read());
        }
        return false;
    }

    // 读取描述信息
    const char* desc = doc["info"]["description"];
    if (desc != nullptr)
    {
        DataDescription = String(desc);
        Serial.println("描述信息: " + DataDescription);
    }

    int index = 0;
    JsonArray dataArray = doc["data"];
    for (JsonObject point : dataArray) {
        if (index >= 600) break;

        load_target[index].t = point["t"].as<int>();
        load_target[index].bt = point["bt"].as<float>();
        load_target[index].fir = point["fir"].as<int>();
        load_target[index].fan = point["fan"].as<int>();

        index++;
    }

    *index_target = index;
    curveFile.close();

    Serial.println("曲线加载成功 ID:" + String(curve_id) + ", 数据点数量:" + String(index));
    return index;  // 返回加载的曲线点数量
}

// 接收JSON字符串并保存到文件
bool save_json_string_to_file(const String& jsonData) {
    if (jsonData.length() == 0) {
        Serial.println("[SAVE_JSON] 错误: JSON数据为空");
        return false;
    }

    // 解析JSON数据
    StaticJsonDocument<2048> doc;
    DeserializationError error = deserializeJson(doc, jsonData);
    if (error) {
        Serial.print("[SAVE_JSON] JSON解析错误: ");
        Serial.println(error.c_str());
        return false;
    }

    // 提取曲线ID
    int curve_id = doc["info"]["id"].as<int>(); // 添加.as<int>()
    if (curve_id < 0 || curve_id > 20) {
        Serial.println("[SAVE_JSON] 错误: 曲线ID超出范围(0-20)");
        return false;
    }

    // 保存曲线数据 - 使用现有的save_curve函数
    DataDescription = doc["info"]["description"].as<String>(); // 添加.as<String>()
    curveIndex = doc["info"]["points"].as<int>(); // 添加.as<int>()
    
    // 解析曲线数据点
    JsonArray dataArray = doc["data"];
    for (int i = 0; i < curveIndex && i < 600; i++) {
        JsonObject point = dataArray[i];
        DataCurve[i].t = point["t"].as<int>();
        DataCurve[i].bt = point["bt"].as<float>();
        DataCurve[i].fir = point["fir"].as<int>();
        DataCurve[i].fan = point["fan"].as<int>();
    }
    
    return save_curve(curve_id, DataCurve, curveIndex);
}

// 发送曲线到指定串口
template<typename SerialType>
bool send_curve_to_serial(SerialType& targetSerial, int curve_id) {
  if constexpr (std::is_same<SerialType, BLECharacteristic*>::value) {
    BLECharacteristic* bleChar = targetSerial;
    String filename = "/curve" + String(curve_id) + ".json";
    File curveFile = SPIFFS.open(filename, "r");
    if (!curveFile) {
      bleChar->setValue("错误: 打开曲线文件失败");
      bleChar->notify();
      return false;
    }

    String jsonString = curveFile.readString();
    curveFile.close();

    bleChar->setValue(jsonString.c_str());
    bleChar->notify();
    return true;
  } else { // For HardwareSerial only (SPP Bluetooth disabled)
      if (curve_id < 0 || curve_id > 20) {
          targetSerial.println("错误: 曲线ID超出范围(0-20)");
          return false;
      }

      String filename = "/curve" + String(curve_id) + ".json";
      File curveFile = SPIFFS.open(filename, "r");
      if (!curveFile) {
          targetSerial.println("错误: 打开曲线文件失败");
          return false;
      }

      String jsonString = curveFile.readString();
      curveFile.close();

      targetSerial.print(jsonString);
      return true;
  }
}

// 显式模板实例化
template bool send_curve_to_serial<HardwareSerial>(HardwareSerial&, int);
// SPP蓝牙已禁用，移除BluetoothSerial实例化
// template bool send_curve_to_serial<BluetoothSerial>(BluetoothSerial&, int);
template bool send_curve_to_serial<BLECharacteristic*>(BLECharacteristic*&, int);
