#ifndef DISPLAY_H
#define DISPLAY_H

// 舵机控制函数声明
void setServoAngle(int channel, int angle);

#include <Arduino.h>
#include <LiquidCrystal_I2C.h>
// SPP蓝牙已禁用
// #include <BluetoothSerial.h>
#include "user_config.h"  // 引入用户配置文件
#include <arduino.h>
#include "file_handler.h"

// 声明外部变量
extern unsigned long roastingTime;        // 原fir_time
extern bool isRoastingStarted;            // 原start_flag
extern int heaterPowerLevel;              // 原levelFIR
extern int fanSpeedLevel;                 // 原levelFAN
extern float beanTemperature;             // 原BT
extern float exhaustTemperature;          // 原ET
extern float rateOfRise;                  // 原ROR
extern bool shouldPromptCurveSave;        // 原save_flag

extern double ct;
extern double Kp;
extern double Ki;
extern double Kd;
extern double targetTemperature;          // 原Setpoint
// SPP蓝牙已禁用
// extern BluetoothSerial SerialBT; // 声明蓝牙串口对象
// BLE相关外部变量声明
#include <BLEServer.h>
extern BLEServer *pBLEServer;
extern machine_state operatingMode;      // 原mode
extern int curve_index;
extern curve_t recordCurve[600];  // 记录曲线数组
//extern int recordCurvePoint;      // 记录曲线点数
extern int recordCurveIndex;      // 曲线ID

// 添加运行曲线相关变量声明
extern curve_t runCurve[600];  // 修改为runCurve
extern int runCurvePoint;      // 运行曲线点数

// 初始化显示
void display_init();

// 更新显示内容
void display_update();

#ifdef USE_LCD_SCREEN
// 定义LCD打印函数
void printValue(int row, int col, const char *label, const char *value);
#endif
// 触摸屏响应相关函数
#ifdef TJC_UART_SCREEN
void check_TJC_Serial();
uint8_t parseIntArray(uint8_t* data, uint8_t length, uint32_t* values);
void handleTJCControl(uint8_t page, uint8_t control, uint8_t* data, uint8_t length);
#endif
#endif // DISPLAY_H