// user_config.h
#ifndef USER_CONFIG_H
#define USER_CONFIG_H

#include <Arduino.h>

// 输出显示设备定义
// 蓝牙配置：SPP蓝牙已禁用，仅使用BLE蓝牙
#define USE_BLE
// #define USE_UART_SCREEN // 使用尚界串口屏
#define TJC_UART_SCREEN // 使用陶晶驰串口屏
// #define USE_TFT_SCREEN  // 使用TFT屏
// #define USE_OLED_SCREEN // 使用OLED屏
// #define USE_LCD_SCREEN  // 使用2004 LCD液晶屏

#define USE_BEAN_SERVO 0  // 1启用排豆舵机功能不能使用无刷普通电调，0禁用排豆，使用普通电调
// 红外测温开关
#define USE_INFRARED_TEMPERATURE 0 // 1启用红外测温功能，0禁用红外测温功能
#include <Adafruit_MLX90614.h>   

// 命令控制优先级
#define HEATER_CMD_PRIORITY 1

// 使用的上位机软件
#define ARTISAN

// 温度检测输入设备定义
#define USE_MAX6675_MODULE // 使用MAX6675 模块
// #define USE_MAX31855_MODULE // 使用MAX31855 模块

// 引脚定义与PCB设计对应-------------------------------------------
// 定义输出接口引脚
#define OUT_DC_FAN 25       // DC风扇PWM输出引脚
#define OUT_FIR 26          // 功率PIN 推荐32,33
#define OUT_LED_WHTE 13     // LED白光输出
#define OUT_LED_YELLOW 12   // LED黄光输出
#define SERVO_PIN 14        // 舵机信号引脚
#define SERVO2_PIN 27        // 舵机2信号引脚靠近电位器

#define SERVO_MIN_PULSE 102 // 舵机0度0.5ms对应占空比
#define SERVO_MAX_PULSE 512 // 舵机180度2.5ms对应占空比
// 定义输入接口引脚
#define IN_FAN_POT 33 // 风扇电位器 IO 34,35,36,39 可以用于输入
#define IN_FIR_POT 35 // 加热电位器
#define IN_LED_POT 32 // LED电位器
#define BUTTON_BT 15  // 选择按钮 
// 定义MAX6675接口引脚
#define thermoCLK 18 // called SCK on the max6675
#define thermoDO 19  // called SO on the max6675
#define thermoCS1 04 // max6675 片选1
#define thermoCS2 05 // max6675 片选2
// 定义串口2的引脚
#define UART2_RX_PIN 16
#define UART2_TX_PIN 17
//----------------------------------------------------------------
#define SERVO_CHAN 2        // 使用LEDC通道2
#define SERVO2_CHAN 4       // 使用LEDC通道3
#define SERVO_FREQ 50       // 50Hz标准频率
#define SERVO_RES 12        // 12位分辨率(0-4095)
#define FIR_FREQ 8000      // 火力PWM频率 - 8kHz，平衡稳定性与噪声控制
#define FIR_RESOLUTION 8  // 火力pwm精度 - 12位(4096级)，与映射范围匹配
#define FIR_PWM_CHAN 5    // 火力pwm通道
#define FAN_FREQ 1600     // 风扇pwm频率 - 恢复为1600Hz
#define FAN_RESULUTION 12 // 风力PWM精度
#define FAN_PWM_CHAN 1    // 风扇pwm通道
#define LED_FREQ 1000     // LED_pwm频率
#define LED_RESULUTION 12 // LED_pwm精度
#define LED_PWM_CHAN 3    // LED_pwm通道

#define FIR_MAX 100           // 最大火力
#define FAN_MAX 100           // 最大风力
#define FAN_HARDWARE_LIMIT 62 // 硬件实际可调风力上限（智能映射）
#define SERVO_MAX 180         // 舵机实际输出 最大角度，也就是输出到无刷电调的最大PWM占空比

// 风扇控制说明：
// 用户界面: 0-100%（提供完整的用户体验）
// 硬件映射: 0-62% （实际硬件可调范围）
// 映射公式: effective_speed = map(user_speed, 0, 100, 0, 62)
#define LED_WHTE_MAX 100      // 最大白光 调整白光黄光最大值可以调整色温
#define LED_YELLOW_MAX 100    // 最大黄光
#define HTR_CUTOFF_FAN_VAL 24  // 安全风力 - 恢复到5%，允许低风力下开火
#define FAN_AUTO_COOL 30      // 使用PID;STOP命令时，为自动冷却设置风扇输出值
#define AUTO_SAVE_TIME 120 // 大于此值时，提示保存曲线 默认120秒即2分钟
// 滤波强度设置
#define FIR_FILTER 10   // FIR电位器的滤波强度
#define FAN_FILTER 10   // FAN电位器的滤波强度
#define LED_FILTER 10   // LED电位器的滤波强度

// 曲线结构体
typedef struct {
    unsigned long t;  // 时间(秒)
    float bt;         // 豆温
    int fir;          // 火力值
    int fan;          // 风力值
} curve_t;
// 曲线数据定义 
// 默认曲线定义
#define DEFAULT_CURVE_ID 0   // 默认曲线ID
#define DEFAULT_CURVE_DESC "测试曲线描述信息"

extern int currentCurveId;  // 当前曲线ID
const curve_t DEFAULT_CURVE[] = {
    {0, 100.0, 20, 30},        // 0秒开火，BT=100，FIR=20，FAN=30
    {5, 105.0, 25, 15},
    {10, 110.0, 30, 20},
    {15, 115.0, 35, 25},
    {20, 120.0, 40, 20},
    {25, 125.0, 45, 15},
    {30, 130.0, 50, 8},
    {35, 135.0, 0, 0}
};

//ET,BT 是否使用卡尔曼滤波，如果不使用，请注释掉下面这行
#define USE_KALMAN_FILTER
#define ROR_WINDOW_SIZE 1         // 设为1表示不使用滑动窗口
#define ROR_FILTER 90          // 超级丝滑：进一步增强ROR滤波强度

// "超级丝滑"方案：进一步优化卡尔曼滤波器参数
#define Q_kalman 0.01f  // 过程噪声协方差：进一步降低，更相信温度变化平缓
#define R_kalman 8.0f   // 测量噪声协方差：进一步提高，认为测量值噪声更大，超强平滑

#define ROR_DISPLAY_LIMIT   250  // ROR显示范围扩大到±400
#define RISE_FILTER         0  // 设为0表示不进行滤波

// 温度变化率限制功能 - 防止温度突然大幅跳变，让曲线更平滑
#define ENABLE_TEMP_RATE_LIMITER        // 启用温度变化率限制

// 动态RoR平滑系统 - "渐进式超级丝滑"方案
#define ENABLE_DYNAMIC_ROR_CONTROL      // 启用动态RoR平滑系统
#define ROR_CONTROL_START_TEMP      40.0f   // 渐进式：提前到40℃开始动态控制，避免突变
#define ROR_CONTROL_END_TEMP        190.0f  // 动态控制结束温度 (℃)

// 渐进式参数：消除4分20秒后的波动
#define ROR_CONTROL_START_MAX_CHANGE 25.0f   // 渐进式：起始变化率放宽，减少控制冲击
#define ROR_CONTROL_END_MAX_CHANGE   1.0f   // 超级丝滑：进一步降低末段变化率

// 渐进式平滑因子：更平缓的过渡
#define ROR_CONTROL_START_SMOOTHING 0.02f   // 渐进式：极低起始平滑度，几乎不干预
#define ROR_CONTROL_END_SMOOTHING   0.95f   // 超级丝滑：极高末段平滑度，消除抖动

// 独立的ET(风温)配置 - 提高灵敏度但保持平滑
#define ET_Q_kalman 0.02f  // ET卡尔曼滤波Q值：比BT稍高，更灵敏
#define ET_R_kalman 4.0f   // ET卡尔曼滤波R值：比BT更低，更快响应

#define TEMP_UPDATE_INTERVAL 900         // 超级丝滑：适度增加采样间隔到900ms，减少高频噪声
#define DISPLAY_UPDATE_INTERVAL 1000     // 显示刷新保持快速响应（10分钟同步显示曲线）
#define CURVE_DISPLAY_INTERVAL 2000     // 调整为2000ms，使曲线与实际烘焙时钟同步
#define POT_UPDATE_INTERVAL 100         // 电位器读取时间间隔
#define CURVE_RECORD_INTERVAL 1000      // 每秒记录一次曲线数据
// PID控制
#define PID_CONTROL
#define PID_CHAN 1 // PID输入的物理通道（对应于热电偶输入T1-T4）
#define CT 1000    // PID控制器的默认采样时间 单位：ms
#define PRO 0.5    // 初始比例参数
#define INT 0.03   // 初始积分参数
#define DER 3.00   // 初始微分参数

enum machine_state
{
  SHOUDONG,
  AUTO,
  BT_AUTO
};
// extern machine_state mode;  // 已移除，现在使用operatingMode

#endif // USER_CONFIG_H