#ifndef GET_TEMP_H
#define GET_TEMP_H
#include <Arduino.h>
#include "FilterLib.h"
#include "user_config.h"
#include <max6675.h> 

// 外部变量声明
extern bool useCelsiusScale;              // 原Cscale
extern float beanTemperature;             // 原BT
extern float exhaustTemperature;          // 原ET
extern float rateOfRise;                  // 原ROR
extern Adafruit_MLX90614 mlx;

void get_samples();

#endif // GET_TEMP_H