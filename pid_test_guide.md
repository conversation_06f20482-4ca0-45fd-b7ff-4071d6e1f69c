# PID控制修复验证指南

## 问题描述
原程序中PID控制可能跟踪的是ET(风温)而不是BT(豆温)，与背景曲线使用的BT不一致。

## 修复内容

### 1. 主要修复
- **确认PID输入温度**：在主循环中明确设置 `Input = beanTemperature` (豆温BT)
- **添加调试输出**：每5秒输出一次PID控制状态，便于监控
- **更新配置注释**：明确PID_CHAN=1对应BT(豆温)

### 2. 新增功能
- **PIDTEST命令**：用于测试和验证PID控制状态
- **详细调试信息**：显示当前温度、目标温度、PID输出等

## 验证步骤

### 步骤1：编译并上传固件
```bash
# 使用Arduino IDE或PlatformIO编译上传修复后的代码
```

### 步骤2：串口测试
打开串口监视器(115200波特率)，发送以下命令：

```
PIDTEST
```

应该看到类似输出：
```
========== PID控制系统测试 ==========
PID模式: 手动
当前输入温度: 25.30℃ (豆温BT)
目标温度: 0.00℃
PID输出: 0.00% (火力)
当前火力: 0%
PID参数: Kp=0.500 Ki=0.030 Kd=3.000
采样时间: 1000ms
注意: PID控制跟踪豆温(BT)，不是风温(ET)
=====================================
```

### 步骤3：启用PID控制测试
```
PID;ON          # 开启PID控制
PID;SV;150      # 设置目标温度为150℃
PIDTEST         # 查看PID状态
```

### 步骤4：观察PID工作状态
启用PID后，每5秒会自动输出调试信息：
```
[PID_DEBUG] 目标温度:150.00 当前豆温:25.30 输出火力:100%
```

### 步骤5：验证温度跟踪
- 观察"当前豆温"字段，确认使用的是BT温度
- 观察火力输出是否根据BT与目标温度的差值进行调节
- 确认不是根据ET(风温)进行控制

## 关键验证点

### ✅ 正确行为
- PID输入显示为"豆温BT"
- 火力根据BT与目标温度差值调节
- 调试信息显示"当前豆温"而不是"当前风温"

### ❌ 错误行为（修复前）
- PID可能跟踪ET(风温)
- 火力调节与BT温度变化不匹配
- 与背景曲线的BT基准不一致

## 其他测试命令

```bash
# 温度监控
TEMPMONITOR     # 开启/关闭温度监控模式

# 火力测试
HEATERTEST,50   # 测试50%火力输出

# PID参数调整
PID;T;1.0,0.05,2.0  # 设置Kp=1.0, Ki=0.05, Kd=2.0
```

## 故障排除

如果PID仍然表现异常：
1. 检查BT传感器连接(引脚4)
2. 确认beanTemperature变量值正常
3. 验证PID库版本兼容性
4. 检查温度滤波器是否影响响应速度

## 技术说明

修复的核心是确保PID控制器的Input变量始终等于beanTemperature(BT)，而不是exhaustTemperature(ET)。这样PID控制就能与烘焙曲线保持一致，都基于豆温进行控制。
