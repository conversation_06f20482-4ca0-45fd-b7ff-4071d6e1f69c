const esbuild = require('esbuild');

// 开发构建
async function devBuild() {
  const result = await esbuild.build({
    entryPoints: ['main.js'],
    bundle: true,
    outfile: 'dist/bundle.js',
    format: 'esm',
    sourcemap: true,
    minify: false,
    watch: {
      onRebuild(error, result) {
        if (error) console.error('构建失败:', error);
        else console.log('构建成功:', new Date());
      },
    },
  });
  console.log('正在监视文件更改...');
}

// 生产构建
async function prodBuild() {
  await esbuild.build({
    entryPoints: ['main.js'],
    bundle: true,
    outfile: 'dist/bundle.js',
    format: 'esm',
    minify: true,
  });
  console.log('生产构建完成');
}

// 根据命令行参数执行不同的构建
const isDev = process.argv.includes('--dev');
if (isDev) {
  devBuild();
} else {
  prodBuild();
} 