#ifndef FILE_HANDLER_H
#define FILE_HANDLER_H

#include <ArduinoJson.h>
#include "user_config.h"
#include <BLECharacteristic.h>
#include <SPIFFS.h>

// 函数声明
bool init_fs();
int save_config_json(const JsonDocument& config);
int load_config_json(JsonDocument& config);
int save_curve(int curve_id, const curve_t* source_curve, int source_index);
int load_curve(int curve_id, curve_t* target_curve, int* target_index);
bool save_json_string_to_file(const String& jsonData);
template<typename SerialType>
bool send_curve_to_serial(SerialType& targetSerial, int curve_id);
bool save_curve_to_spiffs(int id, const curve_t* curve, int point_count);
bool load_curve_from_spiffs(int id, curve_t* curve, int& point_count);

// 外部变量声明
extern curve_t DataCurve[600];
extern int curveIndex;
extern String DataDescription;

#endif // FILE_HANDLER_H