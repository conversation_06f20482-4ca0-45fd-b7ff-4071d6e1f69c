#ifndef GET_POT_H
#define GET_POT_H

#include "user_config.h"
#include "FilterLib.h"
#include <Arduino.h>

// 全局变量 外部变量声明
extern int heaterPowerLevel;              // 原levelFIR
extern int fanSpeedLevel;                 // 原levelFAN
extern int whiteLedBrightness;            // 原level_LED_WHTE
extern int yellowLedBrightness;           // 原level_LED_YELLOW
extern bool isCommandControlActive;       // 原cmd_control_active
extern machine_state operatingMode;      // 原mode

extern int ADC_MAX;
extern const float DEAD_ZONE_THRESHOLD;

extern float prev_fir_pot;
extern float prev_fan_pot;
extern float prev_led_pot;

void update_pot_value(int pin, float &prev_pot, int &level, float max_val, LowPassFilter &lpf);
void get_pot();

#endif // GET_POT_H