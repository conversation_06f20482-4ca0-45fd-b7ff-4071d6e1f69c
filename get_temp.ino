#include "get_temp.h"
#include <vector>
#include <numeric> // Required for std::accumulate if needed later, good practice to include
     // 导入MAX6675库

// 超级丝滑：为ROR添加更强的卡尔曼滤波器
static KalmanFilter rorKalmanFilter(0.05f, 0.8f); // 超级丝滑：更低Q值，更高R值

// "渐进式超级丝滑"方案：为BT和ET添加独立的卡尔曼滤波器
#ifdef USE_KALMAN_FILTER
static KalmanFilter btKalmanFilter(<PERSON>_kalman, R_kalman);
static KalmanFilter etKalmanFilter(ET_Q_kalman, ET_R_kalman);  // ET使用独立参数，更灵敏
#endif

// 温度单调性保证变量
#ifdef ENABLE_MONOTONIC_TEMPERATURE
static float lastMonotonicBT = NAN;    // 上次单调BT温度
static float lastMonotonicET = NAN;    // 上次单调ET温度
static unsigned long lastTempTime = 0; // 上次温度更新时间
static int lastHeaterLevel = -1;      // 上次火力等级
static int lastFanLevel = -1;         // 上次风力等级
#endif

// 中值滤波+移动平均混合滤波器用于ROR额外平滑
class MedianMovingAverage {
private:
    float values[7]; // 保存最近7个值
    float sortedValues[7]; // 用于中值计算的排序数组
    int index;
    int count;
    
public:
    MedianMovingAverage() : index(0), count(0) {
        for (int i = 0; i < 7; i++) {
            values[i] = 0;
            sortedValues[i] = 0;
        }
    }
    
    // 冒泡排序获取中值
    float getMedian(float* arr, int size) {
        // 复制数组以不影响原数据
        for (int i = 0; i < size; i++) {
            sortedValues[i] = arr[i];
        }
        
        // 冒泡排序
        for (int i = 0; i < size - 1; i++) {
            for (int j = 0; j < size - i - 1; j++) {
                if (sortedValues[j] > sortedValues[j + 1]) {
                    float temp = sortedValues[j];
                    sortedValues[j] = sortedValues[j + 1];
                    sortedValues[j + 1] = temp;
                }
            }
        }
        
        // 返回中值
        return sortedValues[size / 2];
    }
    
    float update(float newValue) {
        // 添加新值
        values[index] = newValue;
        index = (index + 1) % 7;
        if (count < 7) count++;
        
        // 先获取中值用于异常值过滤
        float median = getMedian(values, count);
        
        // 异常值过滤 - 如果与中值差距太大则被视为异常值
        for (int i = 0; i < count; i++) {
            if (abs(values[i] - median) > 30) {
                // 限制异常值接近中值
                values[i] = median + (values[i] > median ? 15 : -15);
            }
        }
        
        // 计算平均值 (去除最高和最低)
        float sum = 0;
        float minVal = values[0];
        float maxVal = values[0];
        
        for (int i = 0; i < count; i++) {
            if (values[i] < minVal) minVal = values[i];
            if (values[i] > maxVal) maxVal = values[i];
            sum += values[i];
        }
        
        // 如果有足够的数据点，则移除最高和最低值
        if (count >= 5) {
            sum -= minVal;
            sum -= maxVal;
            return sum / (count - 2);
        } else {
            return sum / count;
        }
    }
}; // Add missing semicolon after class definition

// 创建ROR移动平均滤波器实例
static MedianMovingAverage rorMedianFilter;

#include <math.h> // For abs() and isnan()

// 存储BT历史数据用于ROR计算
// static std::vector<float> bt_history; // 已移至主文件作为全局变量
// static float initial_BT = NAN; // 不再需要初始BT，改用历史数据计算
const int ROR_WINDOW_SECONDS = 60;
const int SAMPLES_IN_ROR_WINDOW = static_cast<int>(ROR_WINDOW_SECONDS * 1000.0f / TEMP_UPDATE_INTERVAL);

// 移除滤波器实例，直接使用原始读数
void get_samples()
{

  static uint32_t startTime = 0; // 记录启动时间
  if (startTime == 0) {
    startTime = millis(); // 首次调用时记录启动时间
  }
  static uint32_t therm_count=0;
  static uint32_t last_therm_count=0;
  static float last_BT = 0;
  static float last_valid_BT = NAN; // Store last valid BT
  static float last_valid_ET = NAN; // Store last valid ET
  // 创建6675对象
  static MAX6675 bt_sensor(thermoCLK, thermoCS1, thermoDO);  // BT传感器
  static MAX6675 et_sensor(thermoCLK, thermoCS2, thermoDO);  // ET传感器


  if (millis() - therm_count > TEMP_UPDATE_INTERVAL)
  {
    therm_count = millis();
    float current_BT_reading, current_ET_reading,current_RT_reading; // Temporary variables for new readings
    
    if (useCelsiusScale)
    { // 获取摄氏度
      // 直接读取原始值
      current_BT_reading = bt_sensor.readCelsius();
      current_ET_reading = et_sensor.readCelsius();
      current_RT_reading = mlx.readObjectTempC();

      // "终极丝滑"方案：使用卡尔曼滤波器进行平滑处理
      if (!isnan(current_BT_reading)) {
        beanTemperature = btKalmanFilter.update(current_BT_reading);
      }
      
      if (!USE_INFRARED_TEMPERATURE) {
        if (!isnan(current_ET_reading)) {
          exhaustTemperature = etKalmanFilter.update(current_ET_reading);
        }
      } else {
        exhaustTemperature = current_RT_reading; // 红外温度不使用卡尔曼滤波
      }
    
      // 应用温度变化率限制（摄氏度版本）
      #ifdef ENABLE_TEMP_RATE_LIMITER
      static float lastBT = NAN;
      static float lastET = NAN;
      static unsigned long lastTempTime = 0;
      static bool rateLimiterInitialized = false;
      
      unsigned long currentTime = millis();
      
      // 初次运行或控制变化时重置
      extern int heaterPowerLevel;
      extern int fanSpeedLevel;
      static int lastHeaterLevel = -1;
      static int lastFanLevel = -1;
      
      if (!rateLimiterInitialized || lastHeaterLevel != heaterPowerLevel || lastFanLevel != fanSpeedLevel) {
        lastBT = beanTemperature;
        lastET = exhaustTemperature;
        lastTempTime = currentTime;
        lastHeaterLevel = heaterPowerLevel;
        lastFanLevel = fanSpeedLevel;
        rateLimiterInitialized = true;
        Serial.printf("[RATE_LIMITER] 初始化/重置：BT=%.2f ET=%.2f\n", lastBT, lastET);
      } else if (currentTime > lastTempTime) {
        float timeInterval = (currentTime - lastTempTime) / 1000.0f; // 时间间隔(秒)
        
        if (timeInterval > 0.1f) { // 至少间隔100ms才处理
          // 处理豆温变化率限制 - 升温降温分别限制
          if (!isnan(lastBT)) {
            float btChange = beanTemperature - lastBT;
            float btChangeRate = btChange / timeInterval; // 每秒变化率
            
            // 根据升温/降温方向设置不同的限制值
            float currentMaxChange;
            if (btChange > 0) {
                // 升温时：最大变化值3度/秒
                currentMaxChange = 3.0f;
            } else {
                // 降温时：最大变化值1度/秒
                currentMaxChange = 1.0f;
            }
            
            if (abs(btChangeRate) > currentMaxChange) {
              // 限制变化率
              float maxChange = currentMaxChange * timeInterval;
              if (btChange > 0) {
                maxChange = min(maxChange, btChange); // 上升时不超过实际变化
              } else {
                maxChange = max(-maxChange, btChange); // 下降时不超过实际变化
              }
              
              // 应用变化限制
              float smoothedBT = lastBT + maxChange;
              
              Serial.printf("[RATE_LIMITER] BT限制(%.0f℃段)：%.2f -> %.2f (原始%.2f，变化率%.1f℃/s，限制%.1f℃/s)\n", 
                            beanTemperature, lastBT, smoothedBT, beanTemperature, btChangeRate, currentMaxChange);
              beanTemperature = smoothedBT;
            }
          }
          
          // 处理风温变化率限制 - 动态RoR控制
          if (!isnan(lastET)) {
            float etChange = exhaustTemperature - lastET;
            float etChangeRate = etChange / timeInterval; // 每秒变化率
            
            // 动态RoR控制: 根据当前温度计算限制参数
            float currentMaxChange, currentSmoothingFactor;
            if (exhaustTemperature < ROR_CONTROL_START_TEMP) {
                currentMaxChange = ROR_CONTROL_START_MAX_CHANGE;
                currentSmoothingFactor = ROR_CONTROL_START_SMOOTHING;
            } else if (exhaustTemperature > ROR_CONTROL_END_TEMP) {
                currentMaxChange = ROR_CONTROL_END_MAX_CHANGE;
                currentSmoothingFactor = ROR_CONTROL_END_SMOOTHING;
            } else {
                // 在动态范围内，线性插值计算当前参数
                float range_fraction = (exhaustTemperature - ROR_CONTROL_START_TEMP) / (ROR_CONTROL_END_TEMP - ROR_CONTROL_START_TEMP);
                currentMaxChange = ROR_CONTROL_START_MAX_CHANGE - range_fraction * (ROR_CONTROL_START_MAX_CHANGE - ROR_CONTROL_END_MAX_CHANGE);
                currentSmoothingFactor = ROR_CONTROL_START_SMOOTHING + range_fraction * (ROR_CONTROL_END_SMOOTHING - ROR_CONTROL_START_SMOOTHING);
            }
            
            if (abs(etChangeRate) > currentMaxChange) {
              // 限制变化率
              float maxChange = currentMaxChange * timeInterval;
              if (etChange > 0) {
                maxChange = min(maxChange, etChange); // 上升时不超过实际变化
              } else {
                maxChange = max(-maxChange, etChange); // 下降时不超过实际变化
              }
              
              // 应用平滑处理
              float smoothedET = lastET + maxChange * currentSmoothingFactor;
              
              Serial.printf("[RATE_LIMITER] ET限制(%.0f℃段)：%.2f -> %.2f (原始%.2f，变化率%.1f℃/s，限制%.1f℃/s)\n", 
                            exhaustTemperature, lastET, smoothedET, exhaustTemperature, etChangeRate, currentMaxChange);
              exhaustTemperature = smoothedET;
            }
          }
          
          // 更新记录
          lastBT = beanTemperature;
          lastET = exhaustTemperature;
          lastTempTime = currentTime;
        }
      }
      #endif
    

    
      // 简单异常检测 (Apply to filtered ET)
      if (exhaustTemperature > 300 || exhaustTemperature < 0) {
        // 如果ET读数异常，仅记录警告，因为滤波应该处理突变
        // delay(10);
        // exhaustTemperature = et_sensor.readCelsius(); // Re-reading bypasses filter
        Serial.println("[WARN] Filtered ET reading is outside expected range (0-300 C)");
      }
      
      //Serial.printf("[TEMP_DEBUG] C BT: %.2f ET: %.2f\n", beanTemperature, exhaustTemperature);
    }
    else
    { // 获取华氏度
      // 直接读取原始值
      current_BT_reading = bt_sensor.readFahrenheit();
      current_ET_reading = et_sensor.readFahrenheit();

      // Filter BT (Threshold: 30C * 1.8 = 54F)
      if (isnan(last_valid_BT) || abs(current_BT_reading - last_valid_BT) <= 54.0) { 
          last_valid_BT = current_BT_reading;
      }
      beanTemperature = last_valid_BT;

      // Filter ET (Threshold: 30C * 1.8 = 54F)
      if (isnan(last_valid_ET) || abs(current_ET_reading - last_valid_ET) <= 54.0) { 
          last_valid_ET = current_ET_reading;
      }
      exhaustTemperature = last_valid_ET;
      
      // 应用温度变化率限制（华氏度版本）
      #ifdef ENABLE_TEMP_RATE_LIMITER
      static float lastBT_F = NAN;
      static float lastET_F = NAN;
      static unsigned long lastTempTime_F = 0;
      static bool rateLimiterInitialized_F = false;
      
      unsigned long currentTime = millis();
      
      // 初次运行或控制变化时重置
      extern int heaterPowerLevel;
      extern int fanSpeedLevel;
      static int lastHeaterLevel_F = -1;
      static int lastFanLevel_F = -1;
      
      if (!rateLimiterInitialized_F || lastHeaterLevel_F != heaterPowerLevel || lastFanLevel_F != fanSpeedLevel) {
        lastBT_F = beanTemperature;
        lastET_F = exhaustTemperature;
        lastTempTime_F = currentTime;
        lastHeaterLevel_F = heaterPowerLevel;
        lastFanLevel_F = fanSpeedLevel;
        rateLimiterInitialized_F = true;
        Serial.printf("[RATE_LIMITER] 初始化/重置（华氏度）：BT=%.2f ET=%.2f\n", lastBT_F, lastET_F);
      } else if (currentTime > lastTempTime_F) {
        float timeInterval = (currentTime - lastTempTime_F) / 1000.0f; // 时间间隔(秒)
        
        if (timeInterval > 0.1f) { // 至少间隔100ms才处理
          // 处理豆温变化率限制 - 升温降温分别限制（华氏度版本）
          if (!isnan(lastBT_F)) {
            float btChange = beanTemperature - lastBT_F;
            float btChangeRate = btChange / timeInterval; // 每秒变化率
            
            // 根据升温/降温方向设置不同的限制值（华氏度）
            float currentMaxChangeF;
            if (btChange > 0) {
                // 升温时：最大变化值3度/秒 * 1.8 = 5.4℉/秒
                currentMaxChangeF = 5.4f;
            } else {
                // 降温时：最大变化值1度/秒 * 1.8 = 1.8℉/秒
                currentMaxChangeF = 1.8f;
            }
            
            if (abs(btChangeRate) > currentMaxChangeF) {
              // 限制变化率
              float maxChange = currentMaxChangeF * timeInterval;
              if (btChange > 0) {
                maxChange = min(maxChange, btChange); // 上升时不超过实际变化
              } else {
                maxChange = max(-maxChange, btChange); // 下降时不超过实际变化
              }
              
              // 应用变化限制
              float smoothedBT = lastBT_F + maxChange;
              
              Serial.printf("[RATE_LIMITER] BT限制(%.0f℉段)：%.2f -> %.2f (原始%.2f，变化率%.1f℉/s，限制%.1f℉/s)\n", 
                            beanTemperature, lastBT_F, smoothedBT, beanTemperature, btChangeRate, currentMaxChangeF);
              beanTemperature = smoothedBT;
            }
          }
          
          // 处理风温变化率限制 - 动态RoR控制
          if (!isnan(lastET_F)) {
            float etChange = exhaustTemperature - lastET_F;
            float etChangeRate = etChange / timeInterval; // 每秒变化率
            
            // 动态RoR控制: 根据当前温度计算限制参数
            float currentMaxChangeF, currentSmoothingFactor;
            if (exhaustTemperature < ROR_CONTROL_START_TEMP) {
                currentMaxChangeF = ROR_CONTROL_START_MAX_CHANGE;
                currentSmoothingFactor = ROR_CONTROL_START_SMOOTHING;
            } else if (exhaustTemperature > ROR_CONTROL_END_TEMP) {
                currentMaxChangeF = ROR_CONTROL_END_MAX_CHANGE;
                currentSmoothingFactor = ROR_CONTROL_END_SMOOTHING;
            } else {
                // 在动态范围内，线性插值计算当前参数
                float range_fraction = (exhaustTemperature - ROR_CONTROL_START_TEMP) / (ROR_CONTROL_END_TEMP - ROR_CONTROL_START_TEMP);
                currentMaxChangeF = ROR_CONTROL_START_MAX_CHANGE - range_fraction * (ROR_CONTROL_START_MAX_CHANGE - ROR_CONTROL_END_MAX_CHANGE);
                currentSmoothingFactor = ROR_CONTROL_START_SMOOTHING + range_fraction * (ROR_CONTROL_END_SMOOTHING - ROR_CONTROL_START_SMOOTHING);
            }
            
            if (abs(etChangeRate) > currentMaxChangeF) {
              // 限制变化率
              float maxChange = currentMaxChangeF * timeInterval;
              if (etChange > 0) {
                maxChange = min(maxChange, etChange); // 上升时不超过实际变化
              } else {
                maxChange = max(-maxChange, etChange); // 下降时不超过实际变化
              }
              
              // 应用平滑处理
              float smoothedET = lastET_F + maxChange * currentSmoothingFactor;
              
              Serial.printf("[RATE_LIMITER] ET限制(%.0f℉段)：%.2f -> %.2f (原始%.2f，变化率%.1f℉/s，限制%.1f℉/s)\n", 
                            exhaustTemperature, lastET_F, smoothedET, exhaustTemperature, etChangeRate, currentMaxChangeF);
              exhaustTemperature = smoothedET;
            }
          }
          
          // 更新记录
          lastBT_F = beanTemperature;
          lastET_F = exhaustTemperature;
          lastTempTime_F = currentTime;
        }
      }
      #endif
      

      
      // 简单异常检测 (Apply to filtered ET)
      if (exhaustTemperature > 572 || exhaustTemperature < 32) { // 华氏度对应的异常范围
        // 如果ET读数异常，仅记录警告
        // delay(10);
        // exhaustTemperature = et_sensor.readFahrenheit(); // Re-reading bypasses filter
        Serial.println("[WARN] Filtered ET reading is outside expected range (32-572 F)");
      }
      
      //Serial.printf("[TEMP_DEBUG] F BT: %.2f ET: %.2f\n", beanTemperature, exhaustTemperature);
    }
    
    // ROR计算触发条件：火力大于0
    extern int heaterPowerLevel;
    if (heaterPowerLevel > 0) {
      // 只有火力大于0时才计算ROR
      float current_BT = beanTemperature;
      
      // 将当前有效BT值添加到历史记录
      if (!isnan(current_BT)) {
          bt_history.push_back(current_BT);
          // 限制历史记录大小，防止内存溢出
          const size_t MAX_HISTORY_SIZE = 3600; // 最大保存1小时的数据
          if (bt_history.size() > MAX_HISTORY_SIZE) { 
              bt_history.erase(bt_history.begin()); 
          }
      }

      float calculated_ror = 0;
      size_t history_size = bt_history.size();

      // 当有足够的数据点后即开始计算ROR (至少2个点)
      // 注意：初期的ROR值可能会因为时间短而跳动较大，依赖卡尔曼滤波器进行平滑
      if (history_size > 1) { 
          if (history_size <= SAMPLES_IN_ROR_WINDOW) {
              // 烘焙初期（60秒内），使用当前窗口内的所有数据计算平均ROR
              float first_bt_in_window = bt_history.front();
              float duration_seconds = (history_size - 1) * (TEMP_UPDATE_INTERVAL / 1000.0f);
              if (duration_seconds > 1e-6) { // 避免除以零
                  calculated_ror = (current_BT - first_bt_in_window) * 60.0f / duration_seconds;
              }
          } else {
              // 烘焙60秒后，使用标准的60秒窗口计算ROR
              float bt_60s_ago = bt_history[history_size - 1 - SAMPLES_IN_ROR_WINDOW];
              calculated_ror = current_BT - bt_60s_ago; // ROR = (T_now - T_60s_ago)
          }
      } else {
          calculated_ror = 0; // 数据不足
      }

      // 应用卡尔曼滤波器平滑ROR
      float filtered_ror = rorKalmanFilter.update(calculated_ror);
      
      // 应用约束，将最终值赋给全局ROR变量
      rateOfRise = constrain(filtered_ror, -ROR_DISPLAY_LIMIT, ROR_DISPLAY_LIMIT);
      
      // 调试信息
      if (history_size > 0 && history_size % 10 == 0) { // 每10次采样输出一次调试信息
          Serial.printf("[ROR_DEBUG] 火力开启 | 历史数据:%zu 原始:%.2f 滤波后:%.2f 最终:%.2f\n", 
                        history_size, calculated_ror, filtered_ror, rateOfRise);
      }

      last_BT = current_BT; 

    } else {
      // 火力为0，ROR立即设为0并清空历史数据
      // 增强规则：在火力为0的状态下不进行ROR计算
      static bool reported_zero_fire = false;
      
      if (!reported_zero_fire) {
        Serial.println("[ROR_RULE] 火力为0，按规则不计算ROR升温率");
        reported_zero_fire = true;
      }
      
      // 立即将ROR设置为0
      rateOfRise = 0;
      
      // 清空历史数据，以便下次开火时重新收集
      if (!bt_history.empty()) {
        bt_history.clear();
        Serial.println("[ROR_DEBUG] 火力为0，重置ROR历史数据");
        reported_zero_fire = false; // 重置报告标志，以便下次火力归零时再次报告
      }
    }
    // 更新上一次的 BT时间戳
    last_therm_count = therm_count;
  }
}