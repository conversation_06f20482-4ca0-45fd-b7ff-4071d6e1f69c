#include "display.h"

// LiquidCrystal_I2C lcd(0x27, 20, 4);
//  初始化显示------------------------------------------------------------
void display_init()
{
#ifdef USE_UART_SCREEN // 使用尚界串口屏
  // 初始化硬件串口2
  Serial2.begin(9600, SERIAL_8N1, UART2_RX_PIN, UART2_TX_PIN);
  Serial2.println("RESET();\r\n");
#endif
#ifdef TJC_UART_SCREEN // 使用陶晶驰串口屏
  // 初始化硬件串口2,陶晶驰屏波特率还有RX，TX都和尚界屏不一样
  Serial2.begin(115200, SERIAL_8N1, UART2_TX_PIN, UART2_RX_PIN);
  Serial2.println("rest \xff\xff\xff");
  Serial2.println("page page0 \xff\xff\xff");
#endif
#ifdef USE_TFT_SCREEN
#endif

#ifdef USE_OLED_SCREEN
#endif

#ifdef USE_LCD_SCREEN
  lcd.init();
  lcd.backlight();
#endif
}

// 更新显示内容---------------------------------------------------------------
void display_update()
{
  static unsigned long lcd_count = 0;         // LCD刷新时间戳
  static unsigned long fir_start_time = 0;    // 开火时间点
  static unsigned long last_curve_update = 0; // 上次曲线更新时间
  static int prev_levelFIR = 0;
  static int prev_levelFAN = 0;
  unsigned long minutes = 0;
  unsigned long seconds = 0;

  // 检查是否达到显示更新的时间间隔
  if (millis() - lcd_count > DISPLAY_UPDATE_INTERVAL)
  {
    // 更新lcd_count以重置计时
    lcd_count = millis();
    // 计算开火时间
    if (isRoastingStarted)
    {
      roastingTime = (millis() - fir_start_time) / 1000; // 将毫秒转换为秒
      minutes = roastingTime / 60;
      seconds = roastingTime % 60;
      // Serial.printf("[TIMER] 当前总秒数：%lu 显示：%02lu:%02lu\n", roastingTime, minutes, seconds);
    }
    else
    {
    }
    // 烘培开始,曲线数组指针清0
    if (prev_levelFIR == 0 && heaterPowerLevel > 0 && isRoastingStarted == false)
    {
      isRoastingStarted = true;
      roastingTime = 0;              // 新锅开始时计时归零
      fir_start_time = millis(); // 重新记录起始时间
      Serial.printf("[TIMER] 开始计时，基准时间：%lu\n", fir_start_time);
      Serial.println("开始烘培!");
      recordCurveIndex = 0;
#ifdef TJC_UART_SCREEN
      // 清除旧曲线数据
      Serial2.print("cle page0.s0.id,255\xff\xff\xff"); // 清除温度曲线
      Serial2.print("cle page0.s1.id,255\xff\xff\xff"); // 清除风火曲线
      
      // 简单清除曲线，不使用可能不支持的高级设置
      // 让屏幕使用默认的曲线配置
#endif
    }
    // 如果火力和风扇都关闭，则烘培结束
    if (isRoastingStarted && heaterPowerLevel == 0 && fanSpeedLevel == 0)
    {
      Serial.println("烘培结束!");
      isRoastingStarted = false;
#ifdef TJC_UART_SCREEN
      // 检查是否需要自动提示保存曲线
      //  如果&& shouldPromptCurveSave == true 则表示开启自动保存，直接保存曲线，2寸屏用
      if (roastingTime > AUTO_SAVE_TIME && recordCurveIndex >= 5 && operatingMode == SHOUDONG && shouldPromptCurveSave == true)
      {
        Serial.printf("[DISPLAY_CURVE_SAVE] 烘焙完成，累计时间：%lu秒，记录了%d个数据点\n", roastingTime, recordCurveIndex);
        // 使用记录曲线数组和currentCurveId保存曲线到对应文件
        if (recordCurveIndex > 0)
        {
          save_curve(currentCurveId, recordCurve, recordCurveIndex);
          Serial.printf("[DISPLAY_CURVE_SAVE] 曲线已保存到ID: %d\n", currentCurveId);
        }
        else
        {
          Serial.println("[DISPLAY_CURVE_SAVE] 警告: 没有可保存的曲线数据");
        }
      }
      // 记录到至少5个数据点并且烘焙时间大于AUTO_SAVE_TIME，才会提示保存4.3寸屏用
      if (roastingTime > AUTO_SAVE_TIME && recordCurveIndex >= 5 && operatingMode == SHOUDONG)
      {
        Serial.printf("[DISPLAY_CURVE_SAVE] 烘焙完成，累计时间：%lu秒，记录了%d个数据点\n", roastingTime, recordCurveIndex);
        // 跳转到保存页面并显示烘焙信息
        Serial2.println("page page2\xff\xff\xff"); // 跳转到曲线保存页面

        // 显示烘焙信息摘要
        char timeStr[20];
        sprintf(timeStr, "%lu分%lu秒", roastingTime / 60, roastingTime % 60);
        Serial2.printf("page2.t0.txt=\"烘焙完成! 时长: %s\"\xff\xff\xff", timeStr);

        // 计算最高温度
        float maxTemp = 0;
        for (int i = 0; i < recordCurveIndex; i++)
        {
          if (recordCurve[i].bt > maxTemp)
          {
            maxTemp = recordCurve[i].bt;
          }
        }
        Serial2.printf("page2.t1.txt=\"最高温度: %.1f°C | 数据点: %d\"\xff\xff\xff", maxTemp, recordCurveIndex);

        // 添加自动保存功能，默认保存到槽位0
        save_curve(0, recordCurve, recordCurveIndex);
        Serial2.printf("page2.t2.txt=\"曲线已自动保存至槽位0\"\xff\xff\xff");

        // 显示返回主页和继续保存的按钮
        //Serial2.println("vis page2.b0,1\xff\xff\xff"); // 显示返回主页按钮
        //Serial2.println("vis page2.b1,1\xff\xff\xff"); // 显示继续保存按钮
#endif
      }
      else
      {
        // 曲线数据点太少或烘焙时间太短，不自动保存
        if (recordCurveIndex < 10)
        {
          Serial.printf("[CURVE_SAVE] 数据点过少(%d)，不保存曲线\n", recordCurveIndex);
        }
        else if (roastingTime <= AUTO_SAVE_TIME)
        {
          Serial.printf("[CURVE_SAVE] 烘焙时间过短(%lu秒)，不保存曲线\n", roastingTime);
        }
      }
    }
    prev_levelFIR = heaterPowerLevel; // 这两行要放在所有检测后面，否则检测不到变化
    prev_levelFAN = fanSpeedLevel; // 记录上一次的火力和风力

    // 限制自动模式下的火力输出
    if (operatingMode == AUTO || operatingMode == BT_AUTO)
    {
      heaterPowerLevel = constrain(heaterPowerLevel, 0, FIR_MAX);
    }

#ifdef USE_UART_SCREEN // 使用尚界串口屏
    // 定义一个字符数组来存储时间字符串
    char timeStr[8];
    char st1[8];
    // 格式化时间字符串为 MM:SS 格式
    snprintf(timeStr, sizeof(timeStr), "%02lu:%02lu", minutes, seconds);
    // 初始化一个字符串缓冲区来构建将要发送的指令
    String buf = "";
    // 构建设置显示屏上各个字段的指令
    buf += "SET_NUM(13,";
    buf += heaterPowerLevel; // 显示火力
    buf += ");";
    buf += "SET_NUM(14,";
    buf += fanSpeedLevel; // 显示风力
    buf += ");";
    buf += "SET_NUM(10,";
    snprintf(st1, sizeof(st1), "%d", int(beanTemperature));
    buf += st1; // 显示BT温度
    buf += ");";
    buf += "SET_NUM(11,";
    buf += int(rateOfRise); // 显示BT温度变化率
    buf += ");";
    buf += "SET_NUM(12,";
    buf += targetTemperature; // 显示PID设定温度
    buf += ");";
    buf += "SET_TXT(9,";
    buf += timeStr; // 显示当前时间
    buf += ");";
    buf += "SET_TXT(2,";
    // 根据PID控制器的模式添加自动或手动状态
    if (myPID.GetMode() == AUTOMATIC)
    {
      buf += "'AUTO'";
    }
    else
    {
      buf += "'MANU'";
    }
    buf += ");";
    buf += "SET_TXT(3,";
    // BLE连接检测
    if (pBLEServer && pBLEServer->getConnectedCount() > 0)
    {
      buf += "'ON'";
    }
    else
    {
      buf += "'OFF'";
    }
    buf += ");\r\n";
    // 通过串口2发送构建好的指令
    Serial2.println(buf);
#endif

#ifdef TJC_UART_SCREEN // 使用陶晶驰串口屏
    // 定义一个字符数组来存储时间字符串
    char timeStr[8];
    char st1[8];
    // 格式化时间字符串为 MM:SS 格式
    snprintf(timeStr, sizeof(timeStr), "%02lu:%02lu", minutes, seconds);
    // 显示开火时间
    Serial2.printf("page0.t7.txt=\"%s\"\xff\xff\xff", timeStr);
    // BLE连接检测
    if (pBLEServer && pBLEServer->getConnectedCount() > 0)
    {
      Serial2.printf("page0.t0.txt=\"已连接\"\xff\xff\xff");
      //Serial2.printf("page0.p0.pic=1\xff\xff\xff"); // 切换已连接图标
      Serial2.println("vis page0.p0.id,1\xff\xff\xff"); // 显示已连接图标
    }
    else
    {
      Serial2.printf("page0.t0.txt=\"未连接\"\xff\xff\xff");
      //Serial2.printf("page0.p0.pic=2\xff\xff\xff"); // 切换未连接图标
      Serial2.println("vis page0.p0.id,0\xff\xff\xff"); // 隐藏已连接图标
    }
    if (operatingMode == SHOUDONG)
    {
      Serial2.printf("page0.t2.txt=\"手动模式\"\xff\xff\xff");
    }
    if (operatingMode == AUTO)
    {
      Serial2.printf("page0.t2.txt=\"本机自动模式\"\xff\xff\xff");
    }
    if (operatingMode == BT_AUTO)
    {
      Serial2.printf("page0.t2.txt=\"蓝牙自动模式\"\xff\xff\xff");
    }
    Serial2.printf("page0.n10.val=%d\xff\xff\xff", int(beanTemperature));
    Serial2.printf("page0.n0.val=%d\xff\xff\xff", int(exhaustTemperature));

    // 高温安全警告
    static bool warnedHighTemp = false;
    if (beanTemperature > 250.0 && !warnedHighTemp)
    {
      Serial2.printf("page0.t11.txt=\"温度已经远高于二爆所需温度，请注意控制火力\"\xff\xff\xff");
      warnedHighTemp = true;
    }
    else if (beanTemperature <= 250.0 && warnedHighTemp)
    {
      Serial2.printf("page0.t11.txt=\"\"\xff\xff\xff");
      warnedHighTemp = false;
    }
    // 更新 ROR 显示
    // Serial.printf("[DISPLAY_DEBUG] Raw ROR: %.2f  Sent: %d\n", ROR, int(ROR));
    // 使用整数传输
    // int ror_for_curve = abs(int(ROR));
    Serial2.printf("page0.n1.val=%d\xff\xff\xff", int(rateOfRise));
    // Serial2.printf("add s0.id,2,%d\xff\xff\xff", ror_for_curve);
    Serial2.printf("page0.n2.val=%d\xff\xff\xff", heaterPowerLevel);
    Serial2.printf("page0.n3.val=%d\xff\xff\xff", fanSpeedLevel);

    // 安全风力警告
    static bool warnedLowFan = false;
    if (heaterPowerLevel >= 80 && fanSpeedLevel <= 40 && !warnedLowFan)
    {
      Serial2.printf("page0.t10.txt=\"40%%风力为安全阈值，请勿低于这个安全风力值\"\xff\xff\xff");
      warnedLowFan = true;
    }
    else if ((heaterPowerLevel < 80 || fanSpeedLevel > 40) && warnedLowFan)
    {
      Serial2.printf("page0.t10.txt=\"\"\xff\xff\xff");
      warnedLowFan = false;
    }
    // 显示曲线
    if (isRoastingStarted && millis() - last_curve_update > CURVE_DISPLAY_INTERVAL) // 根据间隔时间发送曲线数据
    {
      last_curve_update = millis();
      // 确保温度值在转换为整数时不超过屏幕显示范围
      int bt_value = constrain(int(beanTemperature), 0, 255);
      int et_value = constrain(int(exhaustTemperature), 0, 255);
      int ror_value = constrain(int(rateOfRise), 0, 255);
      
      // 恢复使用add命令，通过控制添加点的频率来与时间同步
      // 每秒添加一个点，与实际时间保持一致
      Serial2.printf("add s0.id,0,%d\xff\xff\xff", bt_value);
      Serial2.printf("add s0.id,1,%d\xff\xff\xff", et_value);
      Serial2.printf("add s0.id,2,%d\xff\xff\xff", ror_value);
      Serial2.printf("add page0.s1.id,0,%d\xff\xff\xff", heaterPowerLevel);
      Serial2.printf("add page0.s1.id,1,%d\xff\xff\xff", fanSpeedLevel);
    }
#endif

#ifdef USE_TFT_SCREEN // 使用TFT屏
#endif

#ifdef USE_OLED_SCREEN // 使用OLED屏
#endif

#ifdef USE_LCD_SCREEN // 使用LCD屏
    // 更新 FIR 显示
    printValue(0, 0, "FIR:", "");
    snprintf(st1, sizeof(st1), "%3d", levelFIR);
    printValue(0, 4, "", st1);

    // 更新 FAN 显示
    printValue(1, 0, "FAN:", "");
    snprintf(st1, sizeof(st1), "%3d", levelFAN);
    printValue(1, 4, "", st1);

    // 更新 BT 显示
    printValue(0, 9, "BT:", "");
    snprintf(st1, sizeof(st1), "%5.1f", BT);
    printValue(0, 12, "", st1);

    // 更新 ROR 显示
    printValue(1, 9, "ROR:", "");
    snprintf(st1, sizeof(st1), "%5.1f", ROR);
    printValue(1, 12, "", st1);

    // 更新 StartTime 显示
    unsigned long seconds = fir_open_count / 1000;
    unsigned long minutes = seconds / 60;
    unsigned long hours = minutes / 60;
    minutes %= 60;
    seconds %= 60;
    snprintf(timeStr, sizeof(timeStr), "%lu:%lu:%lu", hours, minutes, seconds);
    printValue(2, 0, "StartTime:", "");
    printValue(2, 10, "", timeStr);

    // 更新模式显示
    // printValue(3, 0, Auto_Ctrl ? "Auto_MODE" : "Manual_MODE", "");

    // 更新 ROR 显示
    printValue(3, 9, "SV:", "");
    snprintf(st1, sizeof(st1), "%5.1f", Setpoint);
    printValue(3, 12, "", st1);
  }
#endif

#ifdef USE_TFT_SCREEN // 使用TFT屏
#endif

#ifdef USE_OLED_SCREEN // 使用OLED屏
#endif

#ifdef USE_LCD_SCREEN // 使用LCD屏
  // 更新 FIR 显示
  printValue(0, 0, "FIR:", "");
  snprintf(st1, sizeof(st1), "%3d", levelFIR);
  printValue(0, 4, "", st1);

  // 更新 FAN 显示
  printValue(1, 0, "FAN:", "");
  snprintf(st1, sizeof(st1), "%3d", levelFAN);
  printValue(1, 4, "", st1);

  // 更新 BT 显示
  printValue(0, 9, "BT:", "");
  snprintf(st1, sizeof(st1), "%5.1f", BT);
  printValue(0, 12, "", st1);

  // 更新 ROR 显示
  printValue(1, 9, "ROR:", "");
  snprintf(st1, sizeof(st1), "%5.1f", ROR);
  printValue(1, 12, "", st1);

  // 更新 StartTime 显示
  unsigned long seconds = fir_open_count / 1000;
  unsigned long minutes = seconds / 60;
  unsigned long hours = minutes / 60;
  minutes %= 60;
  seconds %= 60;
  snprintf(timeStr, sizeof(timeStr), "%lu:%lu:%lu", hours, minutes, seconds);
  printValue(2, 0, "StartTime:", "");
  printValue(2, 10, "", timeStr);

  // 更新模式显示
  // printValue(3, 0, Auto_Ctrl ? "Auto_MODE" : "Manual_MODE", "");

  // 更新 ROR 显示
  printValue(3, 9, "SV:", "");
  snprintf(st1, sizeof(st1), "%5.1f", Setpoint);
  printValue(3, 12, "", st1);
#endif
} // display_update
}
// 更新显示结束----------------------------------------------------------------------

#ifdef TJC_UART_SCREEN
/**
 * @brief 处理TJC串口屏通信
 * 协议格式：命令帧(65+页面+控件+操作符+FF+FF+FF)
 *
 */
// 这个是TJC触摸屏的响应函数
void check_TJC_Serial()
{
  static uint8_t buffer[256];
  static int bufferSize = 0; // 当前缓冲区中的数据长度

  static unsigned long currentTime = millis();
  static unsigned long lastDataTime = 0; // 记录最后一次接收到数据的时间
  while (Serial2.available() > 0)
  {
    uint8_t data = Serial2.read();
    currentTime = millis();
    // 如果距离上次接收数据的时间超过了设定的阈值，认为是新的按键事件
    if (currentTime - lastDataTime > 50)
    {
      // 如果之前有未处理的数据，先尝试处理它
      if (bufferSize > 0)
      {
        if (bufferSize >= 7 && buffer[0] == 0x65)
        {
          uint8_t cmdPage = buffer[1];
          uint8_t cmdControl = buffer[2];
          //Serial.printf("[TJC_DEBUG] 上次命令帧: 页面%d 控件%d \n", cmdPage, cmdControl);
          handleTJCControl(cmdPage, cmdControl, buffer, bufferSize - 7);
          bufferSize = 0; // 处理完数据后立即清空缓冲区
        }
      }
    }
    // 将新接收的数据存入缓冲区
    if (bufferSize < 256)
    {
      buffer[bufferSize++] = data;
    }
    lastDataTime = currentTime; // 更新最后接收时间
  }
  // 循环结束后检查超时并处理剩余数据
  if (bufferSize > 0 && (millis() - lastDataTime > 50))
  {
    if (bufferSize >= 7 && buffer[0] == 0x65)
    {
      uint8_t cmdPage = buffer[1];
      uint8_t cmdControl = buffer[2];
      //Serial.printf("[TJC_DEBUG] 本次命令帧: 页面%d 控件%d \n", cmdPage, cmdControl);
      handleTJCControl(cmdPage, cmdControl, buffer, bufferSize - 7);
    }
    bufferSize = 0; // 处理完剩余数据后清空缓冲区
  }
}

/**
 * @brief 解析TJC数据帧中的整型数组
 * @param data 数据指针
 * @param length 数据长度(字节)
 * @param values 输出数组
 * @return 实际解析的整型数量
 */
uint8_t parseIntArray(uint8_t *data, uint8_t length, uint32_t *values)
{
  uint8_t count = length / 4;
  for (uint8_t i = 0; i < count; i++)
  {
    values[i] = data[i * 4+7] | (data[i * 4 + 8] << 8) | (data[i * 4 + 9] << 16) | (data[i * 4 + 10] << 24);
    //Serial.printf("解析值[%d]: %u (0x%08X)\n", i, values[i], values[i]); // 调试输出
  }
  return count;
}

/**
 * @brief 处理TJC控件数据 触摸屏控件事件都在这里处理
 * 注意：这个函数只处理按下事件，不处理释放事件
 * 只需要在触摸屏开发界面上对需要ESP32处理的控件按下事件，对"发送键值"进行勾选
 * 需要发送额外数据可以在"发送键值"下面的代码部分输入：page1.n0.val  表示发送页面1的控件0的值
 * 然后，在本函数中添加对该控件的处理逻辑即可，
 * 这个部分可以让AI处理，输入提示词：添加处理页面X控件X的逻辑，处理逻辑。
 * @param page 页面号
 * @param control 控件号
 * @param data 数据指针
 * @param length 数据长度(字节)
 */

void handleTJCControl(uint8_t page, uint8_t control, uint8_t *data, uint8_t length)
{
  uint32_t values[50]; // 用于存储解析后的整型值

  // 添加调试信息输出接收到的原始数据
  Serial.printf("[DEBUG] 处理页面%d控件%d 数据长度:%d\n", page, control, length);
  if (length > 0)
  {
    Serial.print("[DEBUG] 原始数据: ");
    for (int i = 0; i < length; i++)
    {
      Serial.printf("%02X ", data[i+7]);
    }
    Serial.println();
  }

  // 2寸屏，页面0 控件7的处理逻辑
  if (page == 0 && control == 7)
  {
    // Serial.printf("[DEBUG] 发送 page1.c0.val 数据，值为: %d\n", save_flag);
    Serial2.printf("page1.c0.val=%d\xff\xff\xff", shouldPromptCurveSave);
    if (operatingMode == SHOUDONG)
    {
      Serial2.printf("page1.b1.txt=\"GO\"\xff\xff\xff");
    }
    else
    {
      Serial2.printf("page1.b1.txt=\"SOTP\"\xff\xff\xff");
      heaterPowerLevel=0;
      fanSpeedLevel=0;
    }
  }
  // 页面0 控件9的处理逻辑 ，4.3寸屏，运行按钮
  if (page == 0 && control == 9 && length >= 4)
  {
    uint8_t parsedCount = parseIntArray(data, length, values);
    load_curve(values[0], runCurve, &runCurvePoint);
    if (operatingMode == SHOUDONG)
    {
      operatingMode = AUTO; // 切换到自动模式
      Serial2.printf("page0.b1.txt=\"放弃\"\xff\xff\xff");
      Serial2.printf("page0.c0.val=0\xff\xff\xff");
      Serial2.printf("page0.c1.val=1\xff\xff\xff");
    }
    else
    {
      operatingMode = SHOUDONG;
      heaterPowerLevel=0;
      fanSpeedLevel=0;
      Serial2.printf("page0.b1.txt=\"本机自动\"\xff\xff\xff");
      Serial2.printf("page0.c0.val=1\xff\xff\xff");
      Serial2.printf("page0.c1.val=0\xff\xff\xff");
    }
    Serial.printf("[TJC] 页面%d 控件%d 按下，加载曲线ID: %u\n", page, control, values[0]);
  }

  // 页面0 控件11的处理逻辑，4.3寸屏，排豆按钮
  if (page == 0 && control == 11)
  {
    // 静态变量，记录舵机状态开启还是关闭
    static bool servoState = false;
    if (USE_BEAN_SERVO){
    // 控制排豆舵机动作
    if (servoState)
    {
      setServoAngle(SERVO2_CHAN, 0);  // 控制舵机角度为0度
      servoState = false;             // 更新舵机状态为关闭
    }else
    {
      setServoAngle(SERVO2_CHAN, 180);  // 控制舵机角度为180度
      servoState = true;               // 更新舵机状态为开启
    }      
    }
  }

  // 2寸屏，页面1 控件3的处理逻辑
  if (page == 1 && control == 3)
  {
    if (length >= 4)
    {
      uint8_t parsedCount = parseIntArray(data, length, values);
      Serial.printf("[DEBUG] 加载曲线ID: %u\n", values[0]);
      load_curve(values[0], runCurve, &runCurvePoint);

      // 调试输出runCurve数组数据
      Serial.println("[DEBUG] 曲线数据:");
      for (int i = 0; i < runCurvePoint; i++)
      {
        Serial.printf("点%d: t=%u, fir=%d, fan=%d\n",
                      i, runCurve[i].t, runCurve[i].fir, runCurve[i].fan);
      }
      if (operatingMode == SHOUDONG)
      {
        operatingMode = AUTO; // 切换到自动模式
        Serial.println("[DEBUG] 模式切换: 手动 -> 自动");
      }
      else
      {
        operatingMode = SHOUDONG;
        Serial.println("[DEBUG] 模式切换: 自动 -> 手动");
      }
    }
    else
    {
      Serial.printf("[ERROR] 页面1控件3数据长度不足: %d\n", length);
    }
  }

  // 页面1 控件4的处理逻辑
  if (page == 1 && control == 4 && length >= 4)
  {
    parseIntArray(data, length, values);
    shouldPromptCurveSave = values[0];
    // 还需要把串口屏选择的曲线号放到全局变量
    currentCurveId = values[1];
  }

  // 页面1 控件7的处理逻辑
  if (page == 1 && control == 7)
  {
    // 控件7的特殊处理逻辑可以在这里添加
  }

  // 页面1 控件42的处理逻辑-读取曲线数据到运行数组 并显示第1页 12个记录点
  if (page == 1 && control == 42 && length >= 4)
  {
    uint8_t parsedCount = parseIntArray(data, length, values);
    Serial.printf("[TJC] 页面%d 控件%d 按下，加载曲线ID: %u\n", page, control, values[0]);
    load_curve(values[0], runCurve, &runCurvePoint);
    // 逐条发送命令字符串
    for (int i = 0; i < 12; i++)
    {
      int displayIndex = i * 3 + 1; // 显示控件从n1开始int displayIndex = i * 3 + 1; // 显示控件从n1开始
      Serial2.print("page1.n" + String(displayIndex) + ".val=" + String(runCurve[i].t) + "\xff\xff\xff");
      Serial2.print("page1.n" + String(displayIndex+1) + ".val=" + String(runCurve[i].fir) + "\xff\xff\xff");
      Serial2.print("page1.n" + String(displayIndex+2) + ".val=" + String(runCurve[i].fan) + "\xff\xff\xff");
    }
  }

  // 页面1 控件43的处理逻辑 - 接收分页曲线数据
  if (page == 1 && control == 43 && length >= 4)
  {
    uint8_t parsedCount = parseIntArray(data, length, values);
    uint8_t curveId = values[0]; // 第一个数据是曲线ID
    uint8_t pageNum = values[1]; // 第二个数据是页号

    // 验证曲线ID和页号合法性
    if (curveId > 20 || pageNum > 49)
    { // 假设最多50页(600/12)
      Serial.printf("[TJC] 错误: 无效曲线ID(%u)或页号(%u)\n", curveId, pageNum);
      return;
    }

    // 每页包含36个数据点(12个记录点)
    for (int i = 0; i < 12; i++)
    {
      int baseIndex = 2 + i * 3; // 每组3个数据(时间,火力,风力)

      // 计算曲线数组中的索引(每页12个点)
      int curveIndex = pageNum * 12 + i;

      // 检查数组边界
      if (curveIndex >= 600)
      {
        Serial.printf("[TJC] 错误: 曲线索引超出范围(%d)\n", curveIndex);
        break;
      }

      // 更新曲线数据
      recordCurve[curveIndex].t = values[baseIndex];
      recordCurve[curveIndex].fir = values[baseIndex + 1];
      recordCurve[curveIndex].fan = values[baseIndex + 2];
    }

    // 保存曲线数据到文件
    save_curve(curveId, recordCurve, (pageNum + 1) * 12); // 保存当前页及之前的所有数据
    Serial.printf("[TJC] 页面%d 控件%d 更新曲线ID: %u 页号: %u 已保存\n", page, control, curveId, pageNum);
  }

  // 页面1 控件46的处理逻辑 - 翻页控件1
  if (page == 1 && control == 46 && length >= 4)
  {
    uint8_t parsedCount = parseIntArray(data, length, values);
    uint8_t pageNum = values[0]; // 第一个数据是页号

    // 显示当前页的数据到串口屏
    String sendBuffer;
    sendBuffer.reserve(12 * 3 * 30 + 200);

    // 计算当前页的起始索引
    int startIndex = pageNum * 12;

    // 检查数据点数量，超出部分发送0
    for (int i = 0; i < 12; i++)
    {
      int displayIndex = i * 3 + 1; // 显示控件从n1开始
      int dataIndex = startIndex + i;

      // 检查是否超出数组范围
      if (dataIndex < runCurvePoint)
      {
        sendBuffer += "page1.n" + String(displayIndex) + ".val=" + String(runCurve[dataIndex].t) + "\xff\xff\xff";
        sendBuffer += "page1.n" + String(displayIndex + 1) + ".val=" + String(runCurve[dataIndex].fir) + "\xff\xff\xff";
        sendBuffer += "page1.n" + String(displayIndex + 2) + ".val=" + String(runCurve[dataIndex].fan) + "\xff\xff\xff";
      }
      else
      {
        // 超出部分发送0
        sendBuffer += "page1.n" + String(displayIndex) + ".val=0\xff\xff\xff";
        sendBuffer += "page1.n" + String(displayIndex + 1) + ".val=0\xff\xff\xff";
        sendBuffer += "page1.n" + String(displayIndex + 2) + ".val=0\xff\xff\xff";
      }
    }
    Serial2.print(sendBuffer); // 发送翻页数据到串口屏
    Serial.printf("[TJC] 页面%d 控件%d 翻页到: %u\n", page, control, pageNum);
  }

  // 页面1 控件47的处理逻辑 - 翻页控件2
  if (page == 1 && control == 47 && length >= 4)
  {
    uint8_t parsedCount = parseIntArray(data, length, values);
    uint8_t pageNum = values[0]; // 第一个数据是页号

    // 显示当前页的数据到串口屏
    String sendBuffer;
    sendBuffer.reserve(12 * 3 * 30 + 200);

    // 计算当前页的起始索引
    int startIndex = pageNum * 12;

    // 检查数据点数量，超出部分发送0
    for (int i = 0; i < 12; i++)
    {
      int displayIndex = i * 3 + 1; // 显示控件从n1开始
      int dataIndex = startIndex + i;

      // 检查是否超出数组范围
      if (dataIndex < runCurvePoint)
      {
        sendBuffer += "page1.n" + String(displayIndex) + ".val=" + String(runCurve[dataIndex].t) + "\xff\xff\xff";
        sendBuffer += "page1.n" + String(displayIndex + 1) + ".val=" + String(runCurve[dataIndex].fir) + "\xff\xff\xff";
        sendBuffer += "page1.n" + String(displayIndex + 2) + ".val=" + String(runCurve[dataIndex].fan) + "\xff\xff\xff";
      }
      else
      {
        // 超出部分发送0
        sendBuffer += "page1.n" + String(displayIndex) + ".val=0\xff\xff\xff";
        sendBuffer += "page1.n" + String(displayIndex + 1) + ".val=0\xff\xff\xff";
        sendBuffer += "page1.n" + String(displayIndex + 2) + ".val=0\xff\xff\xff";
      }
    }
    Serial2.print(sendBuffer); // 发送翻页数据到串口屏
    Serial.printf("[TJC] 页面%d 控件%d 翻页到: %u\n", page, control, pageNum);
  }
  // 页面2 控件2的处理逻辑 - 保存曲线数据
  if (page == 2 && control == 2 && length >= 4)
  {
    uint8_t parsedCount = parseIntArray(data, length, values);
    uint8_t curveId = values[0]; // 第一个数据是曲线ID

    // 保存曲线数据
    if (save_curve(curveId, recordCurve, recordCurveIndex))
    {
      Serial.printf("[TJC] 页面%d 控件%d 曲线ID: %u 已保存\n", page, control, curveId);
      // Serial2.printf("page2.t0.txt=\"曲线ID: %u 保存成功\"\xff\xff\xff", curveId);
    }
    else
    {
      Serial.printf("[ERROR] 页面%d 控件%d 曲线ID: %u 保存失败\n", page, control, curveId);
      // Serial2.printf("page2.t0.txt=\"曲线ID: %u 保存失败\"\xff\xff\xff", curveId);
    }
  }
}
#endif

#ifdef USE_LCD_SCREEN
// 定义LCD打印函数
void printValue(int row, int col, const char *label, const char *value)
{
  // 声明用于格式化输出的字符串变量
  char st1[10];     // 用于存储格式化后的数值字符串
  char timeStr[12]; // 用于存储时间字符串
  lcd.setCursor(col, row);
  lcd.print(label);
  lcd.setCursor(col + strlen(label), row);
  lcd.print(value);
}
#endif