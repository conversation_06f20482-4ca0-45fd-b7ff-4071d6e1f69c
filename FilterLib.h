/*
    1，低通滤波器：
    LowPassFilter.init(float alpha)
    alpha值的范围为[0-100]，越大越平滑，会滞后。

    2，一阶卡尔曼滤波器调整方法
    KalmanFilter(float Q, float R)
    参数     影响        调整策略
    Q       模型不确定性 较大值 → 系统对测量更敏感
    R       测量噪声强度 较大值 → 过滤效果更强（但会滞后）
    float Q = 0.05; // 过程噪声协方差（温度变化快时增大）
    float R = 0.2; // 测量噪声协方差（ADC分辨率低时增大）
    3，滑动窗口滤波器

*/
#ifndef FILTER_LIB_H_
#define FILTER_LIB_H_


// 低通滤波器声明-------------------------------------------------------------
class LowPassFilter
{
public:
    LowPassFilter(float alpha = 30.0f);
    float update(float rawValue);
    void setParams(float alpha);

public:
    int level;  // 滤波强度 [0-100]%
    float y;    // 最新输出值
    bool first; // 首次采样标志
    
    // 获取滤波后的值
    float getFilteredValue() { return y; }
};

// 一阶卡尔曼滤波器声明-------------------------------------------------------
class KalmanFilter
{
public:
    // 构造器：Q-过程噪声, R-测量噪声 - "高响应温度"优化版
    KalmanFilter(float Q = 0.04f, float R = 4.0f);

    // 输入测量值更新状态估计
    float update(float measurement);

    // 动态设置噪声参数（自动过滤非法值）
    void setParams(float Q, float R) noexcept;

private:
    float processNoise;     // 过程噪声协方差 (表征系统模型的不确定性)
    float measurementNoise; // 测量噪声协方差 (表征传感器噪声水平)
    float stateEstimate;    // 状态估计值     (系统的最优估计结果)
    float errorCovariance;  // 误差协方差     (估计结果的可信度指标)
};
//---------------------------------------------------------------------------
class SlidingWindow{
public:
    // 构造函数
    SlidingWindow(int size);

    // 析构函数
    ~SlidingWindow();

    // 更新数据并返回均值
    float update(float new_value);
    
    // 获取窗口平均值
    float average() { return size > 0 ? sum / size : 0.0f; }
    // 新增：获取窗口数据指针和大小
    const float* getWindow() const { return window; }
    int getWindowSize() const { return size; }

private:
    // 定义限幅值常量 - 平衡响应性与平滑度
    const float SLIDING_WINDOW_LIMIT = 4.0f; // 适度提高到4.0，保持温度响应的真实性
    float *window;  // 滑动窗口缓冲区
    int size;       // 窗口大小
    int index;      // 当前索引
    float sum;      // 当前窗口内所有值的和
};

#endif // FILTER_LIB_H_