<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>咖啡烘焙机 Web 控制器</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script type="module" src="dist/bundle.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            min-height: 100vh;
            margin: 0;
            padding: 2rem 0;
            background: #1f2023;
            background-image: linear-gradient(135deg, #2a2d32 0%, #1c1e22 100%);
            color: #e0e0e0;
        }
        .container {
            width: 95%;
            max-width: 1200px;
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            flex-wrap: wrap;
            padding: 1rem;
        }
        h1 {
            color: #e9d8a6;
            margin: 0;
            font-size: 1.8rem;
        }
        #connect-button {
            background-image: linear-gradient(45deg, #e9d8a6, #c8b88a);
            color: #2a2d32;
            font-weight: bold;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
        #connect-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }
        #connect-button:disabled {
            background: #555;
            color: #999;
            cursor: not-allowed;
            transform: translateY(0);
            box-shadow: none;
        }
        .main-layout {
            display: grid;
            grid-template-columns: 3fr 1fr;
            gap: 1.5rem;
        }
        /* Base for all glass cards */
        .glass-card {
            background: rgba(40, 42, 48, 0.65);
            backdrop-filter: blur(12px);
            -webkit-backdrop-filter: blur(12px);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        .charts-area {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }
        .chart-container {
            position: relative;
        }
        #temp-chart-container {
            height: 44vh;
        }
        #power-chart-container {
            height: 22vh;
        }
        .sidebar {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }
        .status-panel, .controls-panel {
            padding: 1.5rem;
        }
        .status-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 1rem;
        }
        .status-item {
            background: rgba(0, 0, 0, 0.2);
            padding: 1rem;
            border-radius: 8px;
            text-align: center;
        }
        .status-item h3 {
            margin: 0 0 0.5rem 0;
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.7);
            text-transform: uppercase;
        }
        .status-item p {
            margin: 0;
            font-size: 2rem;
            font-weight: bold;
            color: #ffffff;
        }
        .time-ror-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }
        .control-group {
            text-align: left;
            margin-bottom: 1.5rem;
        }
        .control-group label {
            display: block;
            margin-bottom: 0.8rem;
            font-weight: bold;
        }
        input[type="range"] {
            -webkit-appearance: none;
            appearance: none;
            width: 100%;
            height: 6px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 3px;
            outline: none;
            transition: opacity .2s;
        }
        input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 18px;
            height: 18px;
            background: #e9d8a6;
            cursor: pointer;
            border-radius: 50%;
        }
        input[type="range"]::-moz-range-thumb {
            width: 18px;
            height: 18px;
            background: #e9d8a6;
            cursor: pointer;
            border-radius: 50%;
        }
        .pid-controls {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        .pid-controls button {
            flex-grow: 1;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            cursor: pointer;
            background-color: rgba(255, 255, 255, 0.1);
            color: #e0e0e0;
            transition: background-color 0.2s;
        }
        .pid-controls button:hover {
            background-color: rgba(255, 255, 255, 0.2);
        }
        @media (max-width: 992px) {
            .main-layout {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>BUU MASTER</h1>
            <div>
                <span id="status-message" style="margin-right: 1rem;">状态：未连接</span>
                <button id="connect-button">连接设备</button>
            </div>
        </div>

        <div class="main-layout">
            <div class="charts-area">
                <div id="temp-chart-container" class="chart-container glass-card">
                    <canvas id="temp-chart"></canvas>
                </div>
                <div id="power-chart-container" class="chart-container glass-card">
                    <canvas id="power-chart"></canvas>
                </div>
            </div>
            <div class="sidebar">
                <div class="status-panel glass-card">
                    <div class="status-grid">
                        <div class="status-item">
                            <h3>豆温</h3>
                            <p id="bean-temp">--</p>
                        </div>
                        <div class="status-item">
                            <h3>风温</h3>
                            <p id="exhaust-temp">--</p>
                        </div>
                    </div>
                    <div class="time-ror-grid">
                         <div class="status-item">
                            <h3>时间</h3>
                            <p id="roast-time">00:00</p>
                        </div>
                        <div class="status-item">
                            <h3>ROR</h3>
                            <p id="ror">--</p>
                        </div>
                    </div>
                </div>
                <div class="controls-panel glass-card">
                    <div class="control-group">
                        <label for="heater-slider">火力: <span id="heater-value">0</span>%</label>
                        <input type="range" id="heater-slider" min="0" max="100" value="0">
                    </div>
                    <div class="control-group">
                        <label for="fan-slider">风力: <span id="fan-value">0</span>%</label>
                        <input type="range" id="fan-slider" min="0" max="100" value="0">
                    </div>
                    <div class="control-group">
                        <label for="sv-input">目标温度: <span id="sv-value">0</span>°C</label>
                        <input type="range" id="sv-input" min="0" max="250" value="0">
                    </div>
                    <div class="control-group">
                        <label>PID 控制</label>
                        <div class="pid-controls">
                            <button id="pid-on-btn">PID ON</button>
                            <button id="pid-off-btn">PID OFF</button>
                            <button id="pid-start-btn">PID START</button>
                            <button id="pid-stop-btn">PID STOP</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const connectButton = document.getElementById('connect-button');
        const statusMessage = document.getElementById('status-message');

        const beanTempElement = document.getElementById('bean-temp');
        const exhaustTempElement = document.getElementById('exhaust-temp');
        const rorElement = document.getElementById('ror');
        const roastTimeElement = document.getElementById('roast-time');

        const heaterSlider = document.getElementById('heater-slider');
        const heaterValue = document.getElementById('heater-value');
        const fanSlider = document.getElementById('fan-slider');
        const fanValue = document.getElementById('fan-value');
        const svInput = document.getElementById('sv-input');
        const svValue = document.getElementById('sv-value');
        
        const pidOnBtn = document.getElementById('pid-on-btn');
        const pidOffBtn = document.getElementById('pid-off-btn');
        const pidStartBtn = document.getElementById('pid-start-btn');
        const pidStopBtn = document.getElementById('pid-stop-btn');

        let bleDevice, bleServer, txCharacteristic, rxCharacteristic;
        let tempChart, powerChart;
        let startTime = null;
        let timeData = [], beanTempData = [], exhaustTempData = [], rorData = [], heaterData = [], fanData = [];

        const SERVICE_UUID = "6e400001-b5a3-f393-e0a9-e50e24dcca9e";
        const CHARACTERISTIC_UUID_RX = "6e400003-b5a3-f393-e0a9-e50e24dcca9e"; // RX from server
        const CHARACTERISTIC_UUID_TX = "6e400002-b5a3-f393-e0a9-e50e24dcca9e"; // TX to server

        function initCharts() {
            const commonOptions = {
                responsive: true,
                maintainAspectRatio: false,
                animation: { duration: 0 },
                layout: {
                    padding: {
                        top: 10,
                        right: 15,
                        bottom: 10,
                        left: 15
                    }
                },
                plugins: {
                    legend: {
                        position: 'top',
                        labels: { color: '#e0e0e0' }
                    },
                    title: {
                        display: false,
                        text: '',
                        color: '#ffffff',
                        font: { size: 16 }
                    }
                }
            };

            const tempCtx = document.getElementById('temp-chart').getContext('2d');
            tempChart = new Chart(tempCtx, {
                type: 'line',
                data: {
                    datasets: [
                        { label: '豆温', data: [], borderColor: '#2ecc71', backgroundColor: 'rgba(46, 204, 113, 0.1)', tension: 0.2, fill: true, pointRadius: 0 },
                        { label: '风温', data: [], borderColor: '#0066cc', backgroundColor: 'rgba(0, 102, 204, 0.1)', tension: 0.2, fill: true, pointRadius: 0 },
                        { label: 'ROR', data: [], borderColor: '#f39c12', backgroundColor: 'rgba(243, 156, 18, 0.0)', tension: 0.2, fill: false, pointRadius: 0, borderDash: [5, 5], yAxisID: 'y-ror' }
                    ]
                },
                options: {
                    ...commonOptions,
                    scales: {
                        x: {
                            type: 'linear',
                            min: 0,
                            max: 10,
                            title: { display: false },
                            ticks: { stepSize: 1, color: '#c0c0c0' },
                            grid: { color: 'rgba(255, 255, 255, 0.1)' }
                        },
                        y: {
                            min: 0,
                            max: 250,
                            afterBuildTicks: axis => {
                                axis.ticks = [
                                    {value: 0, label: '0'},
                                    {value: 50, label: '50'},
                                    {value: 100, label: '100'},
                                    {value: 150, label: '150'},
                                    {value: 200, label: '200'},
                                    {value: 250, label: '250'}
                                ];
                            },
                            ticks: { color: '#c0c0c0' },
                            grid: { color: 'rgba(255, 255, 255, 0.1)' }
                        },
                        'y-ror': {
                            min: -10,
                            max: 40,
                            position: 'right',
                            title: {
                                display: true,
                                text: 'ROR',
                                color: '#f39c12'
                            },
                            ticks: { 
                                color: '#f39c12',
                                stepSize: 10
                            },
                            grid: {
                                drawOnChartArea: false
                            }
                        }
                    }
                }
            });

            const powerCtx = document.getElementById('power-chart').getContext('2d');
            powerChart = new Chart(powerCtx, {
                type: 'line',
                data: {
                    datasets: [
                        { label: '火力', data: [], borderColor: '#e74c3c', backgroundColor: 'rgba(231, 76, 60, 0.1)', tension: 0.2, fill: true, pointRadius: 0 },
                        { label: '风力', data: [], borderColor: '#3498db', backgroundColor: 'rgba(52, 152, 219, 0.1)', tension: 0.2, fill: true, pointRadius: 0 }
                    ]
                },
                options: {
                    ...commonOptions,
                    scales: {
                        x: {
                            type: 'linear',
                            min: 0,
                            max: 10,
                            title: { display: false },
                            ticks: { stepSize: 1, color: '#c0c0c0' },
                            grid: { color: 'rgba(255, 255, 255, 0.1)' }
                        },
                        y: {
                            min: 0,
                            max: 100,
                            afterBuildTicks: axis => {
                                axis.ticks = [
                                    {value: 0, label: '0'},
                                    {value: 25, label: '25'},
                                    {value: 50, label: '50'},
                                    {value: 75, label: '75'},
                                    {value: 100, label: '100'}
                                ];
                            },
                            ticks: { color: '#c0c0c0' },
                            grid: { color: 'rgba(255, 255, 255, 0.1)' }
                        }
                    }
                }
            });
        }

        function resetUI() {
            beanTempElement.textContent = '--';
            exhaustTempElement.textContent = '--';
            rorElement.textContent = '--';
            roastTimeElement.textContent = '00:00';
            heaterSlider.value = 0;
            heaterValue.textContent = 0;
            fanSlider.value = 0;
            fanValue.textContent = 0;
            svInput.value = 0;
            svValue.textContent = 0;
            
            startTime = null;

            if(tempChart) {
                tempChart.data.datasets.forEach(dataset => {
                    dataset.data.length = 0;
                });
                tempChart.update('none');
            }
            smoothedRor = 0;
            if(powerChart) {
                powerChart.data.datasets.forEach(dataset => {
                    dataset.data.length = 0;
                });
                powerChart.update('none');
            }
        }

        window.onload = initCharts;

        connectButton.addEventListener('click', async () => {
            try {
                if (bleDevice && bleDevice.gatt.connected) {
                    bleDevice.gatt.disconnect();
                    return;
                }
                statusMessage.textContent = '正在搜索设备...';
                connectButton.disabled = true;

                bleDevice = await navigator.bluetooth.requestDevice({
                    filters: [{ name: 'BUU4.0' }],
                    optionalServices: [SERVICE_UUID]
                });

                bleDevice.addEventListener('gattserverdisconnected', onDisconnected);
                statusMessage.textContent = '正在连接...';
                bleServer = await bleDevice.gatt.connect();

                statusMessage.textContent = '正在获取服务...';
                const service = await bleServer.getPrimaryService(SERVICE_UUID);

                statusMessage.textContent = '正在获取特征...';
                txCharacteristic = await service.getCharacteristic(CHARACTERISTIC_UUID_TX);
                rxCharacteristic = await service.getCharacteristic(CHARACTERISTIC_UUID_RX);

                statusMessage.textContent = '正在订阅通知...';
                await rxCharacteristic.startNotifications();
                rxCharacteristic.addEventListener('characteristicvaluechanged', handleNotifications);

                statusMessage.textContent = '已连接';
                connectButton.textContent = '断开连接';
                connectButton.disabled = false;
                
                resetUI(); // Reset UI on new connection
                startTime = Date.now();

            } catch (error) {
                console.error('连接失败:', error);
                statusMessage.textContent = `连接失败: ${error.message}`;
                connectButton.disabled = false;
                connectButton.textContent = '连接设备';
            }
        });
        
        function onDisconnected() {
            statusMessage.textContent = '设备已断开';
            connectButton.disabled = false;
            connectButton.textContent = '重新连接';
            resetUI();
        }

        let lastBeanTemp = null;
        let lastTimestamp = null;
        const ROR_SMOOTHING_FACTOR = 0.1;
        let smoothedRor = 0;

        function handleNotifications(event) {
            const value = event.target.value;
            const decoder = new TextDecoder('utf-8');
            const dataString = decoder.decode(value);
            const parts = dataString.trim().split(',');

            if (parts.length >= 7) {
                const [ambientTemp, exhaustTemp, beanTemp, p3, p4, heater, fan, sv] = parts.map(p => parseFloat(p) || 0);

                const now = Date.now();
                if (!startTime) { 
                    startTime = now;
                }
                const elapsedTimeSeconds = (now - startTime) / 1000;
                
                const minutes = Math.floor(elapsedTimeSeconds / 60);
                const seconds = Math.floor(elapsedTimeSeconds % 60);
                roastTimeElement.textContent = `${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
                
                beanTempElement.textContent = beanTemp.toFixed(1);
                exhaustTempElement.textContent = exhaustTemp.toFixed(1);

                if (lastBeanTemp !== null && lastTimestamp !== null) {
                    const tempDiff = beanTemp - lastBeanTemp;
                    const timeDiffSeconds = (now - lastTimestamp) / 1000;
                    if (timeDiffSeconds > 0.2) {
                        const rawRor = (tempDiff / timeDiffSeconds) * 60;
                        smoothedRor = (rawRor * ROR_SMOOTHING_FACTOR) + (smoothedRor * (1 - ROR_SMOOTHING_FACTOR));
                        rorElement.textContent = smoothedRor.toFixed(1);
                        lastBeanTemp = beanTemp;
                        lastTimestamp = now;
                    }
                } else {
                     lastBeanTemp = beanTemp;
                     lastTimestamp = now;
                }

                const elapsedTimeMinutes = elapsedTimeSeconds / 60;
                if (elapsedTimeMinutes > 10) return; // Stop updating chart after 10 mins

                const lastDataPoint = tempChart.data.datasets[0].data[tempChart.data.datasets[0].data.length - 1];
                
                if (!lastDataPoint || elapsedTimeSeconds - (lastDataPoint.x * 60) > 1) {
                    
                    tempChart.data.datasets[0].data.push({x: elapsedTimeMinutes, y: beanTemp});
                    tempChart.data.datasets[1].data.push({x: elapsedTimeMinutes, y: exhaustTemp});
                    tempChart.data.datasets[2].data.push({x: elapsedTimeMinutes, y: smoothedRor});
                    powerChart.data.datasets[0].data.push({x: elapsedTimeMinutes, y: heater});
                    powerChart.data.datasets[1].data.push({x: elapsedTimeMinutes, y: fan});

                    tempChart.update('none');
                    powerChart.update('none');
                }

            } else {
                console.warn('接收到的数据格式不正确:', dataString);
            }
        }

        async function sendCommand(command) {
            if (!txCharacteristic) return console.error('写特征不可用。');
            try {
                const encoder = new TextEncoder();
                await txCharacteristic.writeValue(encoder.encode(command));
            } catch (error) {
                console.error('发送命令失败:', error);
            }
        }
        
        function setupSliderListener(slider, valueLabel, commandPrefix) {
            slider.addEventListener('input', () => {
                valueLabel.textContent = slider.value;
            });
            slider.addEventListener('change', () => {
                // Special check for heater control
                if (commandPrefix === 'OT1' && parseInt(fanSlider.value) <= 25) {
                    alert('请先将风力提高到25%以上再调节火力。');
                    slider.value = 0;
                    valueLabel.textContent = 0;
                    return;
                }
                sendCommand(`${commandPrefix},${slider.value}`);
            });
        }

        setupSliderListener(heaterSlider, heaterValue, 'OT1');
        setupSliderListener(fanSlider, fanValue, 'DCFAN');
        setupSliderListener(svInput, svValue, 'SV');

        pidOnBtn.addEventListener('click', () => sendCommand('PID,ON'));
        pidOffBtn.addEventListener('click', () => sendCommand('PID,OFF'));
        pidStartBtn.addEventListener('click', () => sendCommand('PID,START'));
        pidStopBtn.addEventListener('click', () => sendCommand('PID,STOP'));

    </script>

</body>
</html>