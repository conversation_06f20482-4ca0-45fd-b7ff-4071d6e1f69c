# Coffee21 咖啡烘焙机控制系统

## 项目概述

这是一个基于ESP32的智能咖啡烘焙机控制系统，支持温度监控、PID控制、蓝牙通信、曲线记录等功能。系统采用模块化设计，便于维护和扩展。

## 主要特性

### 🌡️ 温度控制
- 双路MAX6675热电偶温度采集（豆温BT、风温ET）
- 可选MLX90614红外测温支持
- 多种滤波算法（低通、卡尔曼、滑动窗口）
- PID自动温度控制
- ROR（升温速率）实时计算

### 📱 通信功能
- BLE蓝牙4.0通信（已禁用SPP蓝牙）
- 支持Artisan等上位机软件
- 实时数据传输
- 远程控制功能

### 🎛️ 控制接口
- 火力控制（0-100%）
- 风力控制（0-100%）
- LED照明控制（白光+黄光）
- 可选舵机排豆功能
- 电位器手动控制

### 📊 数据记录
- 烘焙曲线自动记录
- JSON格式数据存储
- SPIFFS文件系统
- 曲线加载和回放
- 自动保存功能

### 🖥️ 显示支持
- 陶晶驰串口屏（TJC）
- 尚界串口屏
- LCD2004液晶屏
- OLED显示屏
- TFT彩屏

## 代码结构

```
coffee21-0602BLEok/
├── coffee21-0602BLEok.ino    # 主程序文件 (809行)
├── display.cpp               # 显示控制实现 (754行)
├── display.h                 # 显示控制头文件 (60行)
├── file_handler.cpp          # 文件操作实现 (336行)
├── file_handler.h            # 文件操作头文件 (32行)
├── get_temp.cpp              # 温度采集实现 (246行)
├── get_temp.h                # 温度采集头文件 (16行)
├── get_pot.cpp               # 电位器读取实现 (68行)
├── get_pot.h                 # 电位器读取头文件 (24行)
├── FilterLib.cpp             # 滤波库实现 (116行)
├── FilterLib.h               # 滤波库头文件 (82行)
├── user_config.h             # 用户配置文件 (137行)
├── 发送数据修改曲线示例.txt    # 数据格式示例
└── 曲线JSON示例文件.json      # 曲线数据示例
```

**总代码量：2,680行**

## 核心模块说明

### 1. 主控制模块 (coffee21-0602BLEok.ino)
- 系统初始化和主循环
- BLE蓝牙通信管理
- PID控制逻辑
- 数据日志输出
- 命令解析处理

### 2. 温度采集模块 (get_temp.cpp/h)
- MAX6675热电偶读取
- MLX90614红外测温
- 多重滤波处理
- ROR计算算法
- 异常值检测

### 3. 显示控制模块 (display.cpp/h)
- 多种显示设备支持
- 实时数据更新
- 烘焙状态管理
- 触摸屏交互
- 曲线显示功能

### 4. 文件操作模块 (file_handler.cpp/h)
- SPIFFS文件系统管理
- JSON配置读写
- 烘焙曲线存储
- 数据备份恢复

### 5. 电位器控制模块 (get_pot.cpp/h)
- 模拟量采集
- 滤波处理
- 死区设置
- 手动/自动模式切换

### 6. 滤波算法库 (FilterLib.cpp/h)
- 低通滤波器
- 一阶卡尔曼滤波
- 滑动窗口滤波
- 中值滤波

## 硬件配置

### 主控制器
- ESP32开发板
- 双核240MHz处理器
- 内置WiFi和蓝牙

### 传感器
- MAX6675热电偶模块 x2
- MLX90614红外测温模块（可选）
- 电位器 x3（火力、风力、LED）

### 执行器
- PWM火力控制
- PWM风扇控制
- LED照明控制
- 舵机排豆控制（可选）

### 显示设备
- 串口屏（陶晶驰/尚界）
- LCD2004液晶屏
- OLED显示屏
- TFT彩屏

## 引脚定义

```cpp
// 输出引脚
#define OUT_DC_FAN 25       // DC风扇PWM
#define OUT_FIR 26          // 火力控制PWM
#define OUT_LED_WHTE 13     // LED白光
#define OUT_LED_YELLOW 12   // LED黄光
#define SERVO_PIN 14        // 舵机信号

// 输入引脚
#define IN_FAN_POT 33       // 风扇电位器
#define IN_FIR_POT 35       // 火力电位器
#define IN_LED_POT 32       // LED电位器
#define BUTTON_BT 15        // 选择按钮

// MAX6675引脚
#define thermoCLK 18        // 时钟
#define thermoDO 19         // 数据
#define thermoCS1 04        // 片选1（BT）
#define thermoCS2 05        # 片选2（ET）

// 串口屏引脚
#define UART2_RX_PIN 16
#define UART2_TX_PIN 17
```

## 配置说明

### 编译配置
在 `user_config.h` 中配置功能模块：

```cpp
// 蓝牙配置
#define USE_BLE                    // 启用BLE蓝牙

// 显示设备选择（只能选择一个）
#define TJC_UART_SCREEN           // 陶晶驰串口屏
// #define USE_UART_SCREEN         // 尚界串口屏
// #define USE_LCD_SCREEN          // LCD2004
// #define USE_OLED_SCREEN         // OLED
// #define USE_TFT_SCREEN          // TFT

// 功能开关
#define USE_BEAN_SERVO 0          // 排豆舵机（0=禁用，1=启用）
#define USE_INFRARED_TEMPERATURE 0 // 红外测温（0=禁用，1=启用）
#define HEATER_CMD_PRIORITY 1     // 命令控制优先级
```

### PID参数
```cpp
#define CT 1000     // 采样时间（ms）
#define PRO 0.5     // 比例参数
#define INT 0.03    // 积分参数
#define DER 3.00    // 微分参数
```

### 安全参数
```cpp
#define HTR_CUTOFF_FAN_VAL 40     // 安全风力阈值
#define FAN_AUTO_COOL 30          // 自动冷却风力
#define AUTO_SAVE_TIME 120        // 自动保存时间（秒）
```

## 使用说明

### 1. 硬件连接
1. 按照引脚定义连接所有硬件
2. 确保电源供应充足
3. 检查热电偶极性
4. 测试所有输出设备

### 2. 软件配置
1. 安装Arduino IDE和ESP32开发环境
2. 安装必要的库文件：
   - PID_v1
   - MAX6675
   - Adafruit_MLX90614
   - ArduinoJson
   - LiquidCrystal_I2C
3. 根据硬件配置修改 `user_config.h`
4. 编译并上传程序

### 3. 操作模式

#### 手动模式 (SHOUDONG)
- 通过电位器手动控制火力和风力
- 实时监控温度变化
- 手动记录烘焙曲线

#### 自动模式 (AUTO)
- 加载预设烘焙曲线
- 自动控制火力和风力
- PID温度控制

#### 蓝牙自动模式 (BT_AUTO)
- 通过上位机软件控制
- 实时数据传输
- 远程监控和调整

### 4. 上位机软件
推荐使用Artisan咖啡烘焙软件：
1. 启用BLE蓝牙连接
2. 配置通信参数
3. 实时监控温度曲线
4. 远程控制烘焙参数

## 数据格式

### 温度数据输出格式
```
AT,ET,BT,53,54,FIR,FAN,SV
```
- AT: 环境温度
- ET: 风温
- BT: 豆温
- 53,54: 保留通道
- FIR: 火力值
- FAN: 风力值
- SV: PID设定值

### 曲线数据格式
```json
{
  "info": {
    "id": 0,
    "description": "测试曲线",
    "points": 100
  },
  "data": [
    {"t": 0, "bt": 100.0, "fir": 20, "fan": 30},
    {"t": 5, "bt": 105.0, "fir": 25, "fan": 15}
  ]
}
```

## 维护和升级

### 版本记录
- **v1.0** - 基础功能实现
- **v1.1** - 添加BLE蓝牙支持
- **v1.2** - 优化滤波算法
- **v1.3** - 增加多显示设备支持
- **v1.4** - 完善文件系统功能

### 已知问题
1. ~~SPP蓝牙兼容性问题~~ (已禁用SPP，改用BLE)
2. 高温环境下传感器稳定性需要改进
3. 长时间运行内存使用需要优化

### 待优化功能
- [ ] 增加WiFi网络功能
- [ ] 云端数据同步
- [ ] 移动APP支持
- [ ] 更多烘焙曲线预设
- [ ] 机器学习算法集成
- [ ] 多语言界面支持

### 贡献指南
1. Fork项目仓库
2. 创建功能分支
3. 提交代码更改
4. 创建Pull Request
5. 代码审查和合并

## 技术支持

### 常见问题
1. **温度读数异常**：检查热电偶连接和极性
2. **蓝牙连接失败**：确认设备配对和距离
3. **显示屏无响应**：检查串口配置和波特率
4. **文件系统错误**：重新格式化SPIFFS

### 调试方法
1. 启用串口调试输出
2. 检查各模块初始化状态
3. 监控内存使用情况
4. 分析温度数据波形

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 项目Issues页面
- 技术交流群
- 邮件联系

---

**注意**：使用本系统进行咖啡烘焙时，请注意安全操作，避免烫伤和火灾风险。建议在专业指导下使用。