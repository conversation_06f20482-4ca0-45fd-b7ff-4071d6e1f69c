#include "FilterLib.h"
#include <cstring>
//#include <math.h>

// 低通滤波器---------------------------------------------------------------
LowPassFilter::LowPassFilter(float alpha)  
{
    level = alpha;
    y = 0;
    first = true;
}
void LowPassFilter::setParams(float alpha)
{
    level = alpha;
    first = true;
}
float LowPassFilter::update(float rawValue)
{
    if( first) {
        y = rawValue;
        first = false;
        return y;
      }
      float weightedRawValue  = (float)(100 - level) * (float)rawValue * 0.01;
      float weightedPreviousValue = (float)level * (float)y * 0.01;
      weightedRawValue += weightedPreviousValue;
      return y = ( weightedRawValue );
}
// -------------------------------------------------------------------------------

// 一阶卡尔曼滤波实现---------------------------------------------------------------
KalmanFilter::KalmanFilter(float Q, float R) : 
processNoise((Q > 0) ? Q : 0.05f),  // 参数安全初始化
measurementNoise((R > 0) ? R : 2.0f),
stateEstimate(0.0f), 
errorCovariance(1.0f) {}

void KalmanFilter::setParams(float Q, float R) noexcept 
{
    processNoise = (Q > 0.0f) ? Q : processNoise;  // 非法值保持原参数
    measurementNoise = (R > 0.0f) ? R : measurementNoise;
}
float KalmanFilter::update(float measurement)
{
    errorCovariance += processNoise;                     // 预测误差协方差
    float K = errorCovariance / (errorCovariance + measurementNoise);     // 计算卡尔曼增益
    stateEstimate += K * (measurement - stateEstimate); // 更新状态估计
    errorCovariance *= (1 - K);                // 更新协方差
    return stateEstimate;
}

//-----------------------------------------------------------------------------------
// 滑动窗口滤波器---------------------------------------------------------------------
// 构造函数
SlidingWindow::SlidingWindow(int size) {
    window = new float[size];
    this->size = size;
    index = 0;
    sum = 0.0f;
    memset(window, 0, size * sizeof(float)); // 初始清零 
}

// 析构函数，释放内存
SlidingWindow::~SlidingWindow() {
    delete[] window;
}


// 更新数据并返回均值 
float SlidingWindow::update(float new_value) {
    // 滑动窗口初始化时不进行限幅
    if(sum == 0.0f) {
        window[index] = new_value;
        sum += new_value;
        index = (index + 1) % size;
        return new_value;
    }
    
    // 计算窗口中所有非零值的平均值
    float validSum = 0.0f;
    int validCount = 0;
    for(int i = 0; i < size; i++) {
        if(window[i] != 0.0f) {
            validSum += window[i];
            validCount++;
        }
    }
    float avg = validCount > 0 ? validSum / validCount : new_value;
    
    // 改进的限幅逻辑：允许温度上升的变化率更大，但下降时限制更严格
    // 这符合咖啡烘焙的物理特性，升温可以较快，但自然冷却通常较慢
    float limit = window[index] != 0.0f ? SLIDING_WINDOW_LIMIT : 100.0f;
    
    // 上升时允许更大的变化，下降时限制更严格
    if(new_value > window[index]) {
        // 温度上升，最大变化值3度
        limit = 3.0f;
    } else {
        // 温度下降，最大变化值1度
        limit = 1.0f;
    }
    
    // 应用限幅
    if(window[index] != 0.0f && abs(new_value - window[index]) > limit) {
        new_value = (new_value > window[index]) ? 
                    window[index] + limit : 
                    window[index] - limit;
    }

    sum -= window[index];       // 移除旧数据 
    sum += new_value;           // 加入新数据 
    window[index] = new_value;  // 存储新值 
    index = (index + 1) % size; // 循环指针 

    return sum / size;          // 返回均值
}
// -------------------------------------------------------------------------------------